{"kg_basic_stats": {"total_triples": 727578, "total_entities": 200558, "total_relations": 19, "entity_type_counts": {"Building": 326149, "Land": 81672, "POI": 221820, "Category": 110910, "Other": 86647, "Function": 86647, "Region": 538102, "BusinessCircle": 594, "Morphology": 2187, "LandUse": 428}, "relation_type_counts": {"belongsToLand": 78283, "cateOf": 110910, "hasPhysicalAttribute": 86647, "hasFunction": 86647, "locateAt": 110910, "provideService": 594, "functionalSimilarity": 198441, "connectedTo": 37140, "nearBy": 6708, "densityInfluence": 3579, "belongsToRegion": 526, "flowTransition": 663, "highConvenience": 2737, "hasMorphology": 2187, "hasDominantFunction": 757, "similarFunction": 146, "hasLandUse": 428, "functionalComplementarity": 151, "similarMorphology": 124}, "error_lines": 0, "total_lines_processed": 727578}, "kg_structure": {"entity_type_distribution": {"Region": 538102, "Building": 326149, "POI": 221820, "Category": 110910, "Other": 86647, "Function": 86647, "Land": 81672, "Morphology": 2187, "BusinessCircle": 594, "LandUse": 428}, "relation_type_distribution": {"functionalSimilarity": 198441, "cateOf": 110910, "locateAt": 110910, "hasPhysicalAttribute": 86647, "hasFunction": 86647, "belongsToLand": 78283, "connectedTo": 37140, "nearBy": 6708, "densityInfluence": 3579, "highConvenience": 2737, "hasMorphology": 2187, "hasDominantFunction": 757, "flowTransition": 663, "provideService": 594, "belongsToRegion": 526}, "connection_stats": {"avg_connections": 7.255537051625963, "max_connections": 65137, "min_connections": 1}, "missing_entity_types": ["Transport"]}, "triple_analysis": {"triple_patterns": {"Region-[functionalSimilarity]->Region": 198441, "POI-[cateOf]->Category": 110910, "POI-[locateAt]->Region": 110910, "Building-[hasPhysicalAttribute]->Other": 86647, "Building-[hasFunction]->Function": 86647, "Building-[belongsToLand]->Land": 78283, "Building-[connectedTo]->Building": 37140, "Region-[nearBy]->Region": 6708, "Region-[densityInfluence]->Region": 3579, "Region-[highConvenience]->Region": 2737}, "relation_categories": {"空间关系": ["locateAt", "nearBy"], "层次关系": ["belongsToRegion", "belongsToLand"], "功能关系": ["similarFunction", "functionalComplementarity", "hasFunction", "hasDominantFunction", "functionalSimilarity", "similarMorphology"], "连接关系": ["connectedTo"], "属性关系": ["hasPhysicalAttribute", "hasLandUse", "hasMorphology"], "时间关系": [], "其他关系": ["highConvenience", "flowTransition", "densityInfluence", "cateOf", "provideService"]}, "missing_relations": {"spatial": ["borderBy", "contains"], "hierarchical": ["belongsTo", "partOf", "includes"], "functional": ["complementary"], "connectivity": ["accessibleBy", "linkedTo"]}, "head_relation_patterns": {"Building": {"belongsToLand": 78283, "hasPhysicalAttribute": 86647, "hasFunction": 86647, "connectedTo": 37140, "similarFunction": 146}, "POI": {"cateOf": 110910, "locateAt": 110910}, "BusinessCircle": {"provideService": 594}, "Region": {"functionalSimilarity": 198441, "nearBy": 6708, "densityInfluence": 3579, "flowTransition": 663, "highConvenience": 2737, "hasDominantFunction": 757, "functionalComplementarity": 151}, "Land": {"belongsToRegion": 526, "hasMorphology": 2187, "hasLandUse": 428, "similarMorphology": 124}}, "tail_relation_patterns": {"Land": {"belongsToLand": 78283, "similarMorphology": 124}, "Category": {"cateOf": 110910}, "Other": {"hasPhysicalAttribute": 86647}, "Function": {"hasFunction": 86647}, "Region": {"locateAt": 110910, "provideService": 594, "functionalSimilarity": 198441, "nearBy": 6708, "densityInfluence": 3579, "belongsToRegion": 526, "flowTransition": 663, "highConvenience": 2737, "hasDominantFunction": 757, "functionalComplementarity": 151}, "Building": {"connectedTo": 37140, "similarFunction": 146}, "Morphology": {"hasMorphology": 2187}, "LandUse": {"hasLandUse": 428}}}, "entity_completeness": {"region_entities": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642, 643, 644, 645, 646, 647, 648, 649, 650, 651, 652, 653, 654, 655, 656, 657, 658, 659, 660, 661, 662, 663, 664, 665, 666, 667, 668, 669, 670, 671, 672, 673, 674, 675, 676, 677, 678, 679, 680, 681, 682, 683, 684, 685, 686, 687, 688, 689, 690, 691, 692, 693, 694, 695, 696, 697, 698, 699, 700, 701, 702, 703, 704, 705, 706, 707, 708, 709, 710, 711, 712, 713, 714, 715, 716, 717, 718, 719, 720, 721, 722, 723, 724, 725, 726, 727, 728, 729, 730, 731, 732, 733, 734, 735, 736, 737, 738, 739, 740, 741, 742, 743, 744, 745, 746, 747, 748, 749, 750, 751, 752, 753, 754, 755, 756, 757], "region_count": 757, "building_count": 86647, "poi_count": 110910, "invalid_region_ids": [], "region_id_range": [1, 757]}, "auxiliary_data": {"region_info_count": 757, "total_dataset_ids": 757, "dataset_splits": {"train": 605, "val": 75, "test": 77}, "poi_image_mapping_count": 757}, "id_alignment": {"dataset_ids_count": 757, "alignment_details": {"知识图谱": {"alignment_rate": 1.0, "intersection_count": 757, "missing_count": 0, "extra_count": 0, "missing_ids": [], "extra_ids": []}, "区域信息": {"alignment_rate": 1.0, "intersection_count": 757, "missing_count": 0, "extra_count": 0, "missing_ids": [], "extra_ids": []}, "POI映射": {"alignment_rate": 1.0, "intersection_count": 757, "missing_count": 0, "extra_count": 0, "missing_ids": [], "extra_ids": []}, "卫星图像": {"alignment_rate": 1.0, "intersection_count": 757, "missing_count": 0, "extra_count": 0, "missing_ids": [], "extra_ids": []}, "街景图像": {"alignment_rate": 1.0, "intersection_count": 757, "missing_count": 0, "extra_count": 0, "missing_ids": [], "extra_ids": []}}, "fully_aligned_count": 757, "full_alignment_rate": 1.0}, "functional_completeness": {"function_entities_count": 6, "function_triples_count": 86793, "found_functions": {"住宅功能": ["Func_Residential"], "商业功能": ["Func_Commercial"], "办公功能": ["Func_Office"], "教育功能": [], "医疗功能": [], "交通功能": [], "娱乐功能": [], "服务功能": ["Func_Public"]}, "missing_functions": {"教育功能": ["education", "school", "university", "教育", "学校"], "医疗功能": ["medical", "hospital", "health", "医疗", "医院"], "交通功能": ["transport", "traffic", "station", "交通", "站点"], "娱乐功能": ["entertainment", "recreation", "娱乐", "休闲"]}, "function_coverage_rate": 1.0}, "tail_entity_analysis": {"by_relation": {"belongsToLand": {"Land": 1831}, "cateOf": {"Category": 15}, "hasPhysicalAttribute": {"Other": 9}, "hasFunction": {"Function": 6}, "locateAt": {"Region": 703}, "provideService": {"Region": 371}, "functionalSimilarity": {"Region": 701}, "connectedTo": {"Building": 4872}, "nearBy": {"Region": 670}, "densityInfluence": {"Region": 392}, "belongsToRegion": {"Region": 93}, "flowTransition": {"Region": 191}, "highConvenience": {"Region": 439}, "hasMorphology": {"Morphology": 11}, "hasDominantFunction": {"Region": 4}, "similarFunction": {"Building": 62}, "hasLandUse": {"LandUse": 3}, "functionalComplementarity": {"Region": 36}, "similarMorphology": {"Land": 75}}, "by_type": {"Land": 1837, "Category": 15, "Other": 9, "Function": 6, "Region": 755, "Building": 4880, "Morphology": 11, "LandUse": 3}}, "region_coverage": {"kg_id_range": [1, 757], "dataset_id_range": [1, 757], "kg_density": 1.0, "dataset_density": 1.0, "missing_in_kg_count": 0, "extra_in_kg_count": 0}, "poi_analysis": {"total_categories": 15, "top_categories": {"Cate_Food_Service": 22725, "Cate_Company_Enterprise": 15304, "Cate_Life_Service": 12481, "Cate_Education_Culture": 11997, "Cate_Transportation_Service": 10709, "Cate_Healthcare_Service": 9383, "Cate_Government_Organization": 7016, "Cate_Sports_Recreation": 5628, "Cate_Business_Residential": 5181, "Cate_Accommodation_Service": 4033}, "missing_categories": ["餐饮服务", "购物服务", "生活服务", "体育休闲服务", "医疗保健服务", "住宿服务", "风景名胜", "商务住宅", "政府机构及社会团体", "科教文化服务", "交通设施服务", "金融保险服务", "公司企业"], "category_distribution": {"Cate_Education_Culture": 11997, "Cate_Transportation_Service": 10709, "Cate_Company_Enterprise": 15304, "Cate_Government_Organization": 7016, "Cate_Food_Service": 22725, "Cate_Healthcare_Service": 9383, "Cate_Financial_Service": 3333, "Cate_Sports_Recreation": 5628, "Cate_Business_Residential": 5181, "Cate_Life_Service": 12481, "Cate_Category_8": 981, "Cate_Shopping_Service": 1537, "Cate_Accommodation_Service": 4033, "Cate_Scenic_Spots": 601, "Cate_Auto_Service": 1}}}