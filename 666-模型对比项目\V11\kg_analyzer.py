"""
知识图谱分析器 (增强版) - 包含ID对齐检查
基于kg_analyzer1.py，新增关键ID对齐检查功能
分析三元组结构，统计实体类型，特别关注功能和形态类别
新增功能：地块功能、建筑物详情、土地利用、密度分析、ID对齐检查等
"""

import pandas as pd
import numpy as np
from collections import defaultdict, Counter
from pathlib import Path
import json
import re
from datetime import datetime
import logging
import os

class EnhancedKnowledgeGraphAnalyzer:
    def __init__(self, kg_file_path, config=None, output_dir="./kg_analysis_results"):
        """
        初始化知识图谱分析器
        
        Args:
            kg_file_path: 知识图谱文件路径（三元组格式）
            config: 配置字典，包含各数据源路径
            output_dir: 分析结果输出目录
        """
        self.kg_file_path = kg_file_path
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 默认配置
        self.config = config or self._get_default_config()
        
        # 设置日志系统
        self._setup_logging()
        
        # 存储分析结果
        self.triples = []
        self.entities = set()
        self.relations = set()
        self.entity_types = defaultdict(set)
        self.relation_stats = defaultdict(int)
        self.entity_stats = defaultdict(int)
        
        # ID对齐相关数据
        self.region_info = {}
        self.dataset_splits = {}
        self.poi_image_mapping = {}
        self.all_dataset_ids = []
        
        # 分析结果和问题记录
        self.results = {}
        self.issues = []
        self.recommendations = []
        
        # 实体类型模式 (增强版)
        self.entity_patterns = {
            'Region': r'^Region_',
            'Land': r'^Land_',
            'Building': r'^Building_',
            'POI': r'^POI_',
            'BusinessCircle': r'^BC_',
            'Category': r'^Cate_',
            'Function': r'^Func_',
            'Morphology': r'^Morph_',
            'LandUse': r'^LandUse_',
            'Density': r'^Density_',
            'Height': r'^Height_',
            'Age': r'^Age_',
            'Service': r'^Service_',
            'Transport': r'^Transport_'
        }
        
        # 功能类型映射
        self.function_types = {
            'Func_Residential': '住宅',
            'Func_Commercial': '商业',
            'Func_Office': '办公',
            'Func_Industrial': '工业',
            'Func_Public': '公共',
            'Func_Education': '教育',
            'Func_Medical': '医疗',
            'Func_Cultural': '文化',
            'Func_Sports': '体育',
            'Func_Transport': '交通',
            'Func_Other': '其他'
        }
        
        # 形态类型映射
        self.morphology_types = {
            'Morph_LowRiseLowDensity': '低层低密度',
            'Morph_LowRiseMidDensity': '低层中密度',
            'Morph_LowRiseHighDensity': '低层高密度',
            'Morph_MidRiseLowDensity': '中层低密度',
            'Morph_MidRiseMidDensity': '中层中密度',
            'Morph_MidRiseHighDensity': '中层高密度',
            'Morph_HighRiseLowDensity': '高层低密度',
            'Morph_HighRiseMidDensity': '高层中密度',
            'Morph_HighRiseHighDensity': '高层高密度',
            'Morph_SuperHighRise': '超高层',
            'Morph_Vacant': '空地'
        }

        # 土地利用类型映射
        self.landuse_types = {
            'LandUse_Residential': '居住用地',
            'LandUse_Commercial': '商业用地',
            'LandUse_Industrial': '工业用地',
            'LandUse_Public': '公共设施用地',
            'LandUse_Green': '绿地',
            'LandUse_Transport': '交通用地',
            'LandUse_Water': '水域',
            'LandUse_Agricultural': '农业用地',
            'LandUse_Mixed': '混合用地',
            'LandUse_Other': '其他用地'
        }

        # POI类别映射
        self.poi_categories = {
            'Cate_Food': '餐饮服务',
            'Cate_Shopping': '购物消费',
            'Cate_Life': '生活服务',
            'Cate_Entertainment': '休闲娱乐',
            'Cate_Education': '教育培训',
            'Cate_Medical': '医疗保健',
            'Cate_Transport': '交通设施',
            'Cate_Finance': '金融保险',
            'Cate_Government': '政府机构',
            'Cate_Tourism': '旅游景点',
            'Cate_Sports': '体育健身',
            'Cate_Culture': '文化场所'
        }

        # 关系类型分组
        self.relation_groups = {
            '空间关系': ['borderBy', 'nearBy', 'connectedTo', 'locateAt', 'withinRegion', 'adjacentToLand'],
            '功能关系': ['similarFunction', 'hasFunctionBuilding', 'hasLandUse', 'cateOf'],
            '形态关系': ['hasMorphology', 'morphologySimilar'],
            '服务关系': ['provideService', 'belongTo'],
            '移动关系': ['flowTransition', 'densityInfluences'],
            '归属关系': ['belongsToBuilding', 'belongsToLand'],
            '便利关系': ['highConvenience']
        }

    def _setup_logging(self):
        """设置日志系统"""
        log_filename = f"kg_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        log_path = self.output_dir / log_filename
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_path, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.log_file_path = log_path
        
        self.logger.info("知识图谱分析器初始化完成")
        
    def _get_default_config(self):
        """获取默认配置"""
        return {
            'region2allinfo_path': r"D:\研二\能耗估算\666-模型对比项目\V11\data\shenyang\shenyang_region2allinfo.json",
            'train_idx_path': './data/shenyang/shenyang_zl15_train.csv',
            'val_idx_path': './data/shenyang/shenyang_zl15_valid.csv',
            'test_idx_path': './data/shenyang/shenyang_zl15_test.csv',
            'satellite_image_dir': './data/shenyang/satellite_image/zl15_224/',
            'streetview_image_dir': './data/shenyang/streetview_image/Region/',
            'poi_streetview_filename_path': './data/shenyang/streetview_image/region_5_10_poi_image_filename.json',
        }
        
    def load_knowledge_graph(self):
        """加载知识图谱三元组"""
        print("🔍 加载知识图谱三元组...")
        self.logger.info("开始加载知识图谱三元组")
        
        try:
            with open(self.kg_file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                        
                    parts = line.split('\t')
                    if len(parts) != 3:
                        self.logger.warning(f"第{line_num}行格式错误: {line}")
                        continue
                    
                    head, relation, tail = parts
                    self.triples.append((head, relation, tail))
                    self.entities.add(head)
                    self.entities.add(tail)
                    self.relations.add(relation)
                    
            self.logger.info(f"成功加载 {len(self.triples):,} 个三元组")
            print(f"✅ 成功加载 {len(self.triples):,} 个三元组")
            print(f"   实体数量: {len(self.entities):,}")
            print(f"   关系数量: {len(self.relations):,}")
            
        except FileNotFoundError:
            error_msg = f"文件未找到: {self.kg_file_path}"
            self.logger.error(error_msg)
            print(f"❌ {error_msg}")
            return False
        except Exception as e:
            error_msg = f"加载失败: {e}"
            self.logger.error(error_msg)
            print(f"❌ {error_msg}")
            return False
            
        return True
    
    def classify_entities(self):
        """对实体进行分类"""
        print("\n📊 分类实体类型...")
        self.logger.info("开始分类实体类型")
        
        for entity in self.entities:
            classified = False
            for entity_type, pattern in self.entity_patterns.items():
                if re.match(pattern, entity):
                    self.entity_types[entity_type].add(entity)
                    self.entity_stats[entity_type] += 1
                    classified = True
                    break
            
            if not classified:
                self.entity_types['Unknown'].add(entity)
                self.entity_stats['Unknown'] += 1
        
        # 打印实体类型统计
        print("\n实体类型统计:")
        for entity_type, count in sorted(self.entity_stats.items(), 
                                       key=lambda x: x[1], reverse=True):
            percentage = count / len(self.entities) * 100
            print(f"  {entity_type:<15}: {count:>8,} ({percentage:>5.1f}%)")
            self.logger.info(f"实体类型 - {entity_type}: {count:,} ({percentage:.1f}%)")
    
    def analyze_relations(self):
        """分析关系统计"""
        print("\n🔗 分析关系统计...")
        self.logger.info("开始分析关系统计")
        
        # 统计关系频次
        for head, relation, tail in self.triples:
            self.relation_stats[relation] += 1
        
        # 按频次排序
        sorted_relations = sorted(self.relation_stats.items(), 
                                key=lambda x: x[1], reverse=True)
        
        print("\n关系类型统计:")
        for relation, count in sorted_relations:
            percentage = count / len(self.triples) * 100
            print(f"  {relation:<25}: {count:>7,} ({percentage:>5.1f}%)")
            self.logger.info(f"关系类型 - {relation}: {count:,} ({percentage:.1f}%)")
        
        return sorted_relations

    def load_auxiliary_data(self):
        """加载辅助数据源 - 新增功能"""
        print("\n📁 5. 加载辅助数据源")
        print("-" * 50)
        self.logger.info("开始加载辅助数据源")
        
        # 加载区域信息
        region_info_path = Path(self.config['region2allinfo_path'])
        if region_info_path.exists():
            try:
                with open(region_info_path, 'r', encoding='utf-8') as f:
                    self.region_info = json.load(f)
                print(f"✅ 区域信息加载完成: {len(self.region_info)} 个区域")
                self.logger.info(f"区域信息加载完成: {len(self.region_info)} 个区域")
            except Exception as e:
                error_msg = f"区域信息加载失败: {e}"
                print(f"❌ {error_msg}")
                self.logger.error(error_msg)
        else:
            error_msg = f"区域信息文件不存在: {region_info_path}"
            print(f"❌ {error_msg}")
            self.logger.warning(error_msg)
        
        # 加载数据集划分
        split_files = {
            'train': self.config['train_idx_path'],
            'val': self.config['val_idx_path'],
            'test': self.config['test_idx_path']
        }
        
        all_split_ids = set()
        for split_name, file_path in split_files.items():
            split_path = Path(file_path)
            if split_path.exists():
                try:
                    df = pd.read_csv(split_path)
                    ids = df['BlockID'].tolist()
                    self.dataset_splits[split_name] = ids
                    all_split_ids.update(ids)
                    print(f"✅ {split_name}集加载完成: {len(ids)} 个区域")
                    self.logger.info(f"{split_name}集加载完成: {len(ids)} 个区域")
                except Exception as e:
                    error_msg = f"{split_name}集加载失败: {e}"
                    print(f"❌ {error_msg}")
                    self.logger.error(error_msg)
            else:
                error_msg = f"{split_name}集文件不存在: {split_path}"
                print(f"❌ {error_msg}")
                self.logger.warning(error_msg)
        
        self.all_dataset_ids = sorted(list(all_split_ids))
        
        # 加载POI街景图像映射
        poi_mapping_path = Path(self.config['poi_streetview_filename_path'])
        if poi_mapping_path.exists():
            try:
                with open(poi_mapping_path, 'r', encoding='utf-8') as f:
                    self.poi_image_mapping = json.load(f)
                print(f"✅ POI图像映射加载完成: {len(self.poi_image_mapping)} 个区域")
                self.logger.info(f"POI图像映射加载完成: {len(self.poi_image_mapping)} 个区域")
            except Exception as e:
                error_msg = f"POI图像映射加载失败: {e}"
                print(f"❌ {error_msg}")
                self.logger.error(error_msg)
        else:
            error_msg = f"POI图像映射文件不存在: {poi_mapping_path}"
            print(f"❌ {error_msg}")
            self.logger.warning(error_msg)

    def check_id_alignment(self):
        """检查ID对齐情况 - 新增核心功能"""
        print("\n🔗 6. 关键ID对齐检查")
        print("-" * 50)
        self.logger.info("开始关键ID对齐检查")
        
        # 获取各数据源的ID集合
        kg_region_ids = set()
        for entity in self.entities:
            if entity.startswith('Region_'):
                try:
                    region_id = int(entity.replace('Region_', ''))
                    kg_region_ids.add(region_id)
                except:
                    self.logger.warning(f"无效区域ID格式: {entity}")
        
        # 区域信息ID
        region_info_ids = set()
        for key in self.region_info.keys():
            try:
                region_info_ids.add(int(key))
            except:
                pass
        
        # 数据集ID
        dataset_ids = set(self.all_dataset_ids)
        
        # POI映射ID
        poi_mapping_ids = set()
        for key in self.poi_image_mapping.keys():
            try:
                poi_mapping_ids.add(int(key))
            except:
                pass
        
        # 检查卫星图像文件
        satellite_dir = Path(self.config['satellite_image_dir'])
        satellite_ids = set()
        if satellite_dir.exists():
            for file_path in satellite_dir.glob("*.png"):
                try:
                    block_id = int(file_path.stem)
                    satellite_ids.add(block_id)
                except:
                    pass
        
        # 检查街景图像目录
        streetview_dir = Path(self.config['streetview_image_dir'])
        streetview_ids = set()
        if streetview_dir.exists():
            for dir_path in streetview_dir.iterdir():
                if dir_path.is_dir():
                    try:
                        block_id = int(dir_path.name)
                        streetview_ids.add(block_id)
                    except:
                        pass
        
        print(f"📊 各数据源ID统计:")
        print(f"   - 知识图谱区域ID: {len(kg_region_ids)} 个")
        print(f"   - 区域信息ID: {len(region_info_ids)} 个")
        print(f"   - 数据集划分ID: {len(dataset_ids)} 个")
        print(f"   - POI图像映射ID: {len(poi_mapping_ids)} 个")
        print(f"   - 卫星图像ID: {len(satellite_ids)} 个")
        print(f"   - 街景图像ID: {len(streetview_ids)} 个")
        
        # 记录统计信息到日志
        self.logger.info(f"各数据源ID统计:")
        self.logger.info(f"知识图谱区域ID: {len(kg_region_ids)} 个")
        self.logger.info(f"区域信息ID: {len(region_info_ids)} 个")
        self.logger.info(f"数据集划分ID: {len(dataset_ids)} 个")
        self.logger.info(f"POI图像映射ID: {len(poi_mapping_ids)} 个")
        self.logger.info(f"卫星图像ID: {len(satellite_ids)} 个")
        self.logger.info(f"街景图像ID: {len(streetview_ids)} 个")
        
        # 计算交集和差集
        alignment_results = {}
        
        # 核心对齐：数据集ID vs 各数据源
        core_checks = {
            '知识图谱': kg_region_ids,
            '区域信息': region_info_ids,
            'POI映射': poi_mapping_ids,
            '卫星图像': satellite_ids,
            '街景图像': streetview_ids
        }
        
        print(f"\n🎯 核心ID对齐分析 (以数据集ID为基准):")
        for source_name, source_ids in core_checks.items():
            if source_ids:
                intersection = dataset_ids.intersection(source_ids)
                missing_in_source = dataset_ids - source_ids
                extra_in_source = source_ids - dataset_ids
                
                alignment_rate = len(intersection) / len(dataset_ids) if dataset_ids else 0
                
                print(f"   - {source_name}:")
                print(f"     * 对齐率: {alignment_rate:.1%} ({len(intersection)}/{len(dataset_ids)})")
                
                if missing_in_source:
                    print(f"     * 缺失ID数量: {len(missing_in_source)}")
                    if len(missing_in_source) <= 5:
                        print(f"     * 缺失ID: {sorted(list(missing_in_source))}")
                        self.logger.warning(f"{source_name}缺失ID: {sorted(list(missing_in_source))}")
                    else:
                        self.logger.warning(f"{source_name}缺失ID数量: {len(missing_in_source)}")
                
                if extra_in_source:
                    print(f"     * 额外ID数量: {len(extra_in_source)}")
                    if len(extra_in_source) <= 5:
                        self.logger.info(f"{source_name}额外ID: {sorted(list(extra_in_source))}")
                    else:
                        self.logger.info(f"{source_name}额外ID数量: {len(extra_in_source)}")
                
                alignment_results[source_name] = {
                    'alignment_rate': alignment_rate,
                    'intersection_count': len(intersection),
                    'missing_count': len(missing_in_source),
                    'extra_count': len(extra_in_source),
                    'missing_ids': sorted(list(missing_in_source)),
                    'extra_ids': sorted(list(extra_in_source))
                }
                
                # 记录严重的对齐问题
                if alignment_rate < 0.9:
                    issue = f"{source_name}对齐率过低: {alignment_rate:.1%}"
                    self.issues.append(issue)
                    self.recommendations.append(f"检查{source_name}数据完整性")
                    self.logger.warning(issue)
                
                # 记录详细对齐信息到日志
                self.logger.info(f"{source_name}对齐分析:")
                self.logger.info(f"  对齐率: {alignment_rate:.1%}")
                self.logger.info(f"  交集数量: {len(intersection)}")
                self.logger.info(f"  缺失数量: {len(missing_in_source)}")
                self.logger.info(f"  额外数量: {len(extra_in_source)}")
                
            else:
                print(f"   - {source_name}: 无数据")
                alignment_results[source_name] = {'alignment_rate': 0, 'note': 'No data available'}
                self.logger.warning(f"{source_name}: 无数据")
        
        # 检查完全对齐的ID
        all_sources = [dataset_ids, kg_region_ids, region_info_ids, satellite_ids]
        fully_aligned_ids = set.intersection(*[s for s in all_sources if s])
        
        if fully_aligned_ids:
            full_alignment_rate = len(fully_aligned_ids) / len(dataset_ids) if dataset_ids else 0
            print(f"\n✅ 完全对齐ID数量: {len(fully_aligned_ids)} ({full_alignment_rate:.1%})")
            self.logger.info(f"完全对齐ID数量: {len(fully_aligned_ids)} ({full_alignment_rate:.1%})")
        else:
            print(f"\n❌ 无完全对齐的ID")
            self.issues.append("无完全对齐的区域ID")
            self.recommendations.append("优先解决数据源间的ID不一致问题")
            self.logger.error("无完全对齐的区域ID")
        
        # 分析街区ID覆盖范围
        self._analyze_region_coverage(kg_region_ids, dataset_ids)
        
        self.results['id_alignment'] = {
            'dataset_ids_count': len(dataset_ids),
            'alignment_details': alignment_results,
            'fully_aligned_count': len(fully_aligned_ids),
            'full_alignment_rate': len(fully_aligned_ids) / len(dataset_ids) if dataset_ids else 0,
            'kg_region_ids': sorted(list(kg_region_ids)),
            'dataset_id_range': [min(dataset_ids), max(dataset_ids)] if dataset_ids else [0, 0],
            'kg_id_range': [min(kg_region_ids), max(kg_region_ids)] if kg_region_ids else [0, 0]
        }

    def _analyze_region_coverage(self, kg_region_ids, dataset_ids):
        """分析街区ID覆盖范围 - 新增功能"""
        print(f"\n🗺️ 街区ID覆盖范围分析:")
        self.logger.info("开始分析街区ID覆盖范围")
        
        if kg_region_ids and dataset_ids:
            print(f"   📊 ID范围统计:")
            print(f"      - 知识图谱区域ID范围: [{min(kg_region_ids)}, {max(kg_region_ids)}]")
            print(f"      - 数据集区域ID范围: [{min(dataset_ids)}, {max(dataset_ids)}]")
            print(f"      - 知识图谱区域总数: {len(kg_region_ids)}")
            print(f"      - 数据集区域总数: {len(dataset_ids)}")
            
            # 分析ID分布密度
            kg_span = max(kg_region_ids) - min(kg_region_ids) + 1
            dataset_span = max(dataset_ids) - min(dataset_ids) + 1
            
            kg_density = len(kg_region_ids) / kg_span
            dataset_density = len(dataset_ids) / dataset_span
            
            print(f"      - 知识图谱ID密度: {kg_density:.3f} ({len(kg_region_ids)}/{kg_span})")
            print(f"      - 数据集ID密度: {dataset_density:.3f} ({len(dataset_ids)}/{dataset_span})")
            
            # 记录范围信息到日志
            self.logger.info(f"ID范围分析:")
            self.logger.info(f"知识图谱区域ID范围: [{min(kg_region_ids)}, {max(kg_region_ids)}]")
            self.logger.info(f"数据集区域ID范围: [{min(dataset_ids)}, {max(dataset_ids)}]")
            self.logger.info(f"知识图谱ID密度: {kg_density:.3f}")
            self.logger.info(f"数据集ID密度: {dataset_density:.3f}")
            
            # 找出缺失和多余的ID
            missing_in_kg = sorted(list(dataset_ids - kg_region_ids))
            extra_in_kg = sorted(list(kg_region_ids - dataset_ids))
            
            print(f"\n   🎯 ID对齐详情:")
            print(f"      - 数据集中缺失于知识图谱的ID数: {len(missing_in_kg)}")
            if missing_in_kg and len(missing_in_kg) <= 10:
                print(f"        缺失ID: {missing_in_kg}")
            elif missing_in_kg:
                print(f"        缺失ID示例: {missing_in_kg[:5]}...{missing_in_kg[-5:]}")
            
            print(f"      - 知识图谱中多余的ID数: {len(extra_in_kg)}")
            if extra_in_kg and len(extra_in_kg) <= 10:
                print(f"        多余ID: {extra_in_kg}")
            elif extra_in_kg:
                print(f"        多余ID示例: {extra_in_kg[:5]}...{extra_in_kg[-5:]}")
                
            # 记录对齐详情到日志
            self.logger.info(f"ID对齐详情:")
            self.logger.info(f"缺失于知识图谱的ID数: {len(missing_in_kg)}")
            self.logger.info(f"知识图谱中多余的ID数: {len(extra_in_kg)}")
            if missing_in_kg:
                self.logger.warning(f"缺失ID: {missing_in_kg}")
            if extra_in_kg:
                self.logger.info(f"多余ID: {extra_in_kg}")

    def analyze_function_morphology_details(self):
        """详细分析功能和形态类别"""
        print("\n🏗️ 详细分析功能和形态类别...")
        self.logger.info("开始详细分析功能和形态类别")
        
        # 功能类别详细分析
        function_entities = self.entity_types.get('Function', set())
        morphology_entities = self.entity_types.get('Morphology', set())
        
        print(f"\n功能类别详情 (共 {len(function_entities)} 种):")
        if function_entities:
            function_usage = defaultdict(int)
            for head, relation, tail in self.triples:
                if tail in function_entities:
                    function_usage[tail] += 1
            
            # 按使用频次排序
            sorted_functions = sorted(function_usage.items(), 
                                    key=lambda x: x[1], reverse=True)
            
            for func_id, count in sorted_functions:
                func_name = self.function_types.get(func_id, func_id)
                percentage = count / sum(function_usage.values()) * 100 if function_usage.values() else 0
                print(f"  {func_id:<25} ({func_name:<8}): {count:>5} 次 ({percentage:>5.1f}%)")
                self.logger.info(f"功能类别 - {func_id} ({func_name}): {count} 次 ({percentage:.1f}%)")
        else:
            print("  未发现功能类别实体")
            self.logger.warning("未发现功能类别实体")
        
        print(f"\n形态类别详情 (共 {len(morphology_entities)} 种):")
        if morphology_entities:
            morphology_usage = defaultdict(int)
            for head, relation, tail in self.triples:
                if tail in morphology_entities:
                    morphology_usage[tail] += 1
            
            # 按使用频次排序
            sorted_morphologies = sorted(morphology_usage.items(), 
                                       key=lambda x: x[1], reverse=True)
            
            for morph_id, count in sorted_morphologies:
                morph_name = self.morphology_types.get(morph_id, morph_id)
                percentage = count / sum(morphology_usage.values()) * 100 if morphology_usage.values() else 0
                print(f"  {morph_id:<30} ({morph_name:<12}): {count:>5} 次 ({percentage:>5.1f}%)")
                self.logger.info(f"形态类别 - {morph_id} ({morph_name}): {count} 次 ({percentage:.1f}%)")
        else:
            print("  未发现形态类别实体")
            self.logger.warning("未发现形态类别实体")
        
        return function_entities, morphology_entities

    def analyze_landuse_categories(self):
        """分析土地利用类别详情"""
        print("\n🗺️ 分析土地利用类别详情...")
        self.logger.info("开始分析土地利用类别详情")

        landuse_entities = self.entity_types.get('LandUse', set())

        if not landuse_entities:
            print("  未发现土地利用类别实体")
            self.logger.warning("未发现土地利用类别实体")
            return None

        print(f"\n土地利用类别详情 (共 {len(landuse_entities)} 种):")

        # 统计土地利用使用频次
        landuse_usage = defaultdict(int)
        landuse_relations = defaultdict(list)

        for head, relation, tail in self.triples:
            if tail in landuse_entities:
                landuse_usage[tail] += 1
                landuse_relations[tail].append((head, relation))

        # 按使用频次排序
        sorted_landuses = sorted(landuse_usage.items(), key=lambda x: x[1], reverse=True)

        for landuse_id, count in sorted_landuses:
            landuse_name = self.landuse_types.get(landuse_id, landuse_id)
            percentage = count / sum(landuse_usage.values()) * 100 if landuse_usage.values() else 0

            # 统计关联的地块数量
            related_lands = set()
            for head, relation in landuse_relations[landuse_id]:
                if head.startswith('Land_'):
                    related_lands.add(head)

            print(f"  {landuse_id:<25} ({landuse_name:<12}): {count:>5} 次, {len(related_lands):>4} 地块 ({percentage:>5.1f}%)")
            self.logger.info(f"土地利用 - {landuse_id} ({landuse_name}): {count} 次, {len(related_lands)} 地块 ({percentage:.1f}%)")

        return landuse_entities, landuse_usage

    def generate_comprehensive_report(self):
        """生成综合报告"""
        print("\n📝 生成综合报告...")
        self.logger.info("开始生成综合报告")
        
        # 创建基础统计报告
        basic_stats = {
            'total_triples': len(self.triples),
            'total_entities': len(self.entities),
            'total_relations': len(self.relations),
            'entity_type_counts': dict(self.entity_stats),
            'relation_counts': dict(self.relation_stats),
            'issues_found': len(self.issues),
            'recommendations_made': len(self.recommendations)
        }
        
        # 保存基础统计到JSON
        json_path = self.output_dir / 'kg_analysis_basic_stats.json'
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(basic_stats, f, ensure_ascii=False, indent=2)
        
        # 保存ID对齐结果
        if hasattr(self, 'results') and 'id_alignment' in self.results:
            id_alignment_path = self.output_dir / 'id_alignment_results.json'
            with open(id_alignment_path, 'w', encoding='utf-8') as f:
                json.dump(self.results['id_alignment'], f, ensure_ascii=False, indent=2)
        
        # 生成综合分析报告
        report_content = self._create_comprehensive_text_report()
        report_path = self.output_dir / f'comprehensive_analysis_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt'
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"✅ 综合报告已生成:")
        print(f"   📄 基础统计: {json_path}")
        if hasattr(self, 'results') and 'id_alignment' in self.results:
            print(f"   📄 ID对齐结果: {id_alignment_path}")
        print(f"   📄 综合报告: {report_path}")
        print(f"   📄 详细日志: {self.log_file_path}")
        
        self.logger.info("综合报告生成完成")
        
        return report_path

    def _create_comprehensive_text_report(self):
        """创建综合文本报告"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        report = f"""
{'='*80}
知识图谱增强版分析报告
{'='*80}
生成时间: {timestamp}
知识图谱文件: {self.kg_file_path}

1. 基本统计信息
{'-'*50}
三元组总数: {len(self.triples):,}
实体总数: {len(self.entities):,}
关系类型数: {len(self.relations):,}

2. 实体类型分布
{'-'*50}
"""
        
        # 实体类型统计
        total_entity_mentions = sum(self.entity_stats.values())
        for entity_type, count in sorted(self.entity_stats.items(), key=lambda x: x[1], reverse=True):
            percentage = count / total_entity_mentions * 100
            report += f"{entity_type:<15}: {count:>8,} ({percentage:>5.1f}%)\n"
        
        # 关系类型统计
        report += f"""
3. 关系类型分布 (前10)
{'-'*50}
"""
        sorted_relations = sorted(self.relation_stats.items(), key=lambda x: x[1], reverse=True)[:10]
        for relation, count in sorted_relations:
            percentage = count / len(self.triples) * 100
            report += f"{relation:<25}: {count:>7,} ({percentage:>5.1f}%)\n"
        
        # ID对齐分析结果
        if hasattr(self, 'results') and 'id_alignment' in self.results:
            id_data = self.results['id_alignment']
            report += f"""
4. ID对齐分析结果
{'-'*50}
数据集区域总数: {id_data['dataset_ids_count']:,}
完全对齐ID数量: {id_data['fully_aligned_count']:,}
完全对齐率: {id_data['full_alignment_rate']:.1%}

各数据源对齐情况:
"""
            for source, data in id_data['alignment_details'].items():
                if isinstance(data, dict) and 'alignment_rate' in data:
                    rate = data['alignment_rate']
                    status = "✅" if rate >= 0.9 else "⚠️" if rate >= 0.7 else "❌"
                    report += f"  {status} {source:<12}: {rate:>6.1%} ({data['intersection_count']}/{id_data['dataset_ids_count']})\n"
                    if data['missing_count'] > 0:
                        report += f"    缺失ID: {data['missing_count']} 个\n"
                    if data['extra_count'] > 0:
                        report += f"    额外ID: {data['extra_count']} 个\n"
            
            # 街区ID范围信息
            if id_data['kg_id_range'][0] > 0:
                report += f"""
街区ID覆盖范围:
  知识图谱ID范围: [{id_data['kg_id_range'][0]}, {id_data['kg_id_range'][1]}]
  数据集ID范围: [{id_data['dataset_id_range'][0]}, {id_data['dataset_id_range'][1]}]
"""
        
        # 功能和形态类别统计
        function_count = len(self.entity_types.get('Function', set()))
        morphology_count = len(self.entity_types.get('Morphology', set()))
        landuse_count = len(self.entity_types.get('LandUse', set()))
        
        report += f"""
5. 详细类别统计
{'-'*50}
建筑功能类别: {function_count} 种
建筑形态类别: {morphology_count} 种
土地利用类别: {landuse_count} 种
POI类别: {len(self.entity_types.get('Category', set()))} 种
"""
        
        # 问题和建议
        report += f"""
6. 发现的问题
{'-'*50}
"""
        if self.issues:
            for i, issue in enumerate(self.issues, 1):
                report += f"{i}. {issue}\n"
        else:
            report += "未发现严重问题。\n"
        
        report += f"""
7. 修复建议
{'-'*50}
"""
        if self.recommendations:
            for i, rec in enumerate(self.recommendations, 1):
                report += f"{i}. {rec}\n"
        else:
            report += "暂无特别建议。\n"
        
        report += f"""
{'='*80}
报告结束 - 详细信息请查看日志文件
{'='*80}
"""
        
        return report

    def run_full_analysis(self):
        """运行完整分析 (增强版)"""
        print("🚀 开始知识图谱完整分析 (增强版)")
        print("=" * 80)
        self.logger.info("开始知识图谱完整分析")

        start_time = datetime.now()

        # 1. 加载数据
        if not self.load_knowledge_graph():
            return False

        # 2. 分类实体
        print("\n" + "="*50)
        self.classify_entities()

        # 3. 分析关系
        print("\n" + "="*50)
        self.analyze_relations()

        # 4. 详细分析功能和形态
        print("\n" + "="*50)
        self.analyze_function_morphology_details()

        # 5. 分析土地利用类别
        print("\n" + "="*50)
        self.analyze_landuse_categories()

        # 6. 加载辅助数据源
        print("\n" + "="*50)
        self.load_auxiliary_data()

        # 7. 关键ID对齐检查 - 核心新增功能
        print("\n" + "="*50)
        self.check_id_alignment()

        # 8. 生成综合报告
        print("\n" + "="*50)
        self.generate_comprehensive_report()

        print("\n" + "=" * 80)
        print("🎉 知识图谱增强版分析完成！")

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        print(f"⏱️ 总耗时: {duration:.1f} 秒")
        
        self.logger.info(f"知识图谱分析完成，总耗时: {duration:.1f} 秒")
        self.logger.info(f"发现问题: {len(self.issues)} 个")
        self.logger.info(f"提出建议: {len(self.recommendations)} 个")

        return True


def main():
    """主函数 (增强版)"""
    # 配置文件路径
    kg_file_path = r"D:\研二\能耗估算\666-模型对比项目\V11\data\shenyang\kg_cuda_complete_21_relations_optimized.txt"
    output_dir = r"D:\研二\能耗估算\666-模型对比项目\V11\kg_analysis_results"

    # 数据源配置
    config = {
        'region2allinfo_path': r"D:\研二\能耗估算\666-模型对比项目\V11\data\shenyang\shenyang_region2allinfo.json",
        'train_idx_path': r'D:\研二\能耗估算\666-模型对比项目\V11\data\shenyang\shenyang_zl15_train.csv',
        'val_idx_path': r'D:\研二\能耗估算\666-模型对比项目\V11\data\shenyang\shenyang_zl15_valid.csv',
        'test_idx_path': r'D:\研二\能耗估算\666-模型对比项目\V11\data\shenyang\shenyang_zl15_test.csv',
        'satellite_image_dir': r'D:\研二\能耗估算\666-模型对比项目\V11\data\shenyang\satellite_image\zl15_224',
        'streetview_image_dir': r'D:\研二\能耗估算\666-模型对比项目\V11\data\shenyang\streetview_image\Region',
        'poi_streetview_filename_path': r'D:\研二\能耗估算\666-模型对比项目\V11\data\shenyang\streetview_image\region_5_10_poi_image_filename.json',
    }

    print("🚀 启动增强版知识图谱分析器")
    print("支持的分析功能:")
    print("  ✅ 基础实体和关系统计")
    print("  ✅ 建筑功能类别详情分析")
    print("  ✅ 建筑形态类别详情分析")
    print("  ✅ 土地利用类别详情分析")
    print("  ✅ 关键ID对齐检查 ⭐️新增")
    print("  ✅ 街区ID覆盖范围分析 ⭐️新增")
    print("  ✅ 详细日志记录系统 ⭐️新增")
    print("  ✅ 综合分析报告生成 ⭐️新增")

    # 创建分析器
    analyzer = EnhancedKnowledgeGraphAnalyzer(kg_file_path, config, output_dir)

    # 运行增强版分析
    success = analyzer.run_full_analysis()

    if success:
        print("\n📋 增强版分析总结:")
        print(f"   三元组总数: {len(analyzer.triples):,}")
        print(f"   实体总数: {len(analyzer.entities):,}")
        print(f"   关系类型数: {len(analyzer.relations):,}")
        print(f"   实体类型数: {len(analyzer.entity_stats):,}")

        # ID对齐结果总结
        if hasattr(analyzer, 'results') and 'id_alignment' in analyzer.results:
            id_results = analyzer.results['id_alignment']
            print(f"\n🔗 ID对齐分析总结:")
            print(f"   数据集区域总数: {id_results['dataset_ids_count']:,}")
            print(f"   完全对齐ID数量: {id_results['fully_aligned_count']:,}")
            print(f"   完全对齐率: {id_results['full_alignment_rate']:.1%}")
            
            if id_results['kg_id_range'][0] > 0:
                print(f"   知识图谱ID范围: [{id_results['kg_id_range'][0]}, {id_results['kg_id_range'][1]}]")
                print(f"   数据集ID范围: [{id_results['dataset_id_range'][0]}, {id_results['dataset_id_range'][1]}]")

        # 详细类别统计
        function_count = len(analyzer.entity_types.get('Function', set()))
        morphology_count = len(analyzer.entity_types.get('Morphology', set()))
        landuse_count = len(analyzer.entity_types.get('LandUse', set()))
        category_count = len(analyzer.entity_types.get('Category', set()))

        print(f"\n🎯 详细类别统计:")
        print(f"   建筑功能类别: {function_count} 种")
        print(f"   建筑形态类别: {morphology_count} 种")
        print(f"   土地利用类别: {landuse_count} 种")
        print(f"   POI类别: {category_count} 种")

        print(f"\n🎉 分析完成！请查看输出目录: {output_dir}")
        print(f"📋 主要文件:")
        print(f"   📄 kg_analysis_basic_stats.json - 基础统计数据")
        if hasattr(analyzer, 'results') and 'id_alignment' in analyzer.results:
            print(f"   📄 id_alignment_results.json - ID对齐详细结果")
        print(f"   📄 comprehensive_analysis_report_*.txt - 综合分析报告")
        print(f"   📄 {analyzer.log_file_path.name} - 详细分析日志")
        
        # 输出关键问题和建议
        if analyzer.issues:
            print(f"\n🚨 发现问题: {len(analyzer.issues)} 个")
            for i, issue in enumerate(analyzer.issues[:3], 1):
                print(f"   {i}. {issue}")
            if len(analyzer.issues) > 3:
                print(f"   ... 及其他 {len(analyzer.issues)-3} 个问题")
        
        if analyzer.recommendations:
            print(f"\n💡 修复建议: {len(analyzer.recommendations)} 个")
            for i, rec in enumerate(analyzer.recommendations[:3], 1):
                print(f"   {i}. {rec}")
            if len(analyzer.recommendations) > 3:
                print(f"   ... 及其他 {len(analyzer.recommendations)-3} 个建议")

    else:
        print("\n❌ 分析过程中出现错误，请检查文件路径和格式")


if __name__ == "__main__":
    main()