# 沈阳城市能耗预测数据集说明文档

## 📋 数据集概述

**数据集名称**: 沈阳城市能耗预测数据集
**数据集类型**: 多模态城市能耗预测
**任务类型**: 回归预测
**生成时间**: 2025-06-05 11:02:48

## 📁 数据文件结构

### 核心数据文件

| 文件类型 | 文件路径 | 格式 | 用途 |
|---------|----------|------|------|
| 知识图谱三元组数据 | `D:\研二\能耗估算\666-模型对比项目\V11\data\shenyang\kg_cuda_complete_21_relations_optimized.txt` | TSV文件 (制表符分隔) | 构建城市区域间的关系网络 |
| 区域详细信息和标签 | `D:\研二\能耗估算\666-模型对比项目\V11\data\shenyang\shenyang_region2allinfo.json` | JSON文件 | 提供区域的多维特征和能耗标签 |
| POI街景图像文件名映射 | `D:\研二\能耗估算\666-模型对比项目\V11\data\shenyang\streetview_image\region_5_10_poi_image_filename.json` | JSON文件 | 建立区域与街景图像文件的对应关系 |
| 训练集区域ID | `D:\研二\能耗估算\666-模型对比项目\V11\data\shenyang\shenyang_zl15_train.csv` | CSV文件 | 指定用于模型训练的区域 |
| 验证集区域ID | `D:\研二\能耗估算\666-模型对比项目\V11\data\shenyang\shenyang_zl15_valid.csv` | CSV文件 | 模型验证和超参数调优 |
| 测试集区域ID | `D:\研二\能耗估算\666-模型对比项目\V11\data\shenyang\shenyang_zl15_test.csv` | CSV文件 | 最终模型性能评估 |
| 卫星遥感图像 | `D:\研二\能耗估算\666-模型对比项目\V11\data\shenyang\satellite_image` | PNG图像文件 | 提供区域的空间视觉特征 |
| 街景图像 | `D:\研二\能耗估算\666-模型对比项目\V11\data\shenyang\streetview_image` | 目录结构的JPG/PNG文件 | 提供区域的街道级视觉特征 |
| 预训练知识图谱嵌入 | `D:\研二\能耗估算\666-模型对比项目\V11\data\shenyang\预训练数据\ER_shenyangbb_TuckER_64.npz` | NPZ文件 (numpy压缩格式) | 提供实体和关系的预训练向量表示 |

## 📊 数据集统计

| 统计项 | 数值 |
|--------|------|
| 总区域数量 | 757 |
| 训练区域数 | 605 |
| 验证区域数 | 75 |
| 测试区域数 | 77 |
| 区域ID范围 | [1, 757] |
| 知识图谱规模 | 200558 实体, 19 关系, 727578 三元组 |
| POI图像映射 | 757 区域, 30280 张图像 |
| 数据集划分比例 | 训练79.9% : 验证9.9% : 测试10.2% |

## 🔄 数据使用流程

1. 加载知识图谱 (kg_without_building.txt) 构建城市区域关系网络
2. 读取区域信息 (region2allinfo.json) 获取特征和能耗标签
3. 根据数据集划分文件 (train/val/test.csv) 分配训练数据
4. 加载预训练嵌入 (TuckER_64.npz) 初始化模型参数
5. 可选加载卫星和街景图像增强特征表示
6. 训练图神经网络进行能耗预测

## 📋 数据字段说明

### 区域信息字段 (region2allinfo.json)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| energy | float | **能耗标签** - 目标预测变量 |
| n_pois | int | POI兴趣点数量 |
| pop | float | 人口数量 |
| edu | float | 教育水平指标 |
| income | float | 收入水平指标 |
| center | list | 区域中心坐标 [经度, 纬度] |
| cd | float | 建筑密度 |
| crime | float | 犯罪率 |
| *_cate | int | 对应字段的分类编码 |

### 知识图谱结构 (kg_without_building.txt)

**图谱规模**: 200,558 实体, 19 关系, 727,578 三元组

**实体类型分布**:
- Other: 694,454 (47.7%)
- Region: 537,345 (36.9%)
- POI: 221,820 (15.2%)
- Facility: 1,537 (0.1%)

**三元组格式**:
```
head_entity    relation    tail_entity
Region_150     near_to     POI_商场_001
Region_150     has_poi     POI_餐厅_002
POI_学校_003   located_in  Region_152
```

**常见关系类型**:
- functionalSimilarity: 198,441 (27.3%)
- cateOf: 110,910 (15.2%)
- locateAt: 110,910 (15.2%)
- hasPhysicalAttribute: 86,647 (11.9%)
- hasFunction: 86,647 (11.9%)
- belongsToLand: 78,283 (10.8%)
- connectedTo: 37,140 (5.1%)
- nearBy: 6,708 (0.9%)
- densityInfluence: 3,579 (0.5%)
- highConvenience: 2,737 (0.4%)

### POI图像映射 (region_5_10_poi_image_filename.json)

**映射统计**: 757 个区域, 30,280 张街景图像

**文件结构**:
JSON格式示例:
```json
{
  "150": ["poi_image_001.jpg", "poi_image_002.jpg"],
  "152": ["poi_image_003.jpg", "poi_image_004.jpg"]
}
```

**图像分布**:
- 平均每区域: 40.0 张图像
- 图像数量范围: [40, 40]

## 🎯 适用场景

- 城市能耗预测和分析
- 多模态城市数据融合研究
- 图神经网络在城市计算中的应用
- 知识图谱增强的预测任务
- 城市空间数据挖掘
- 智慧城市能源管理系统

## 💡 使用建议

### 快速开始

Python代码示例:
```python
# 1. 加载配置
from config import get_config
config = get_config('shenyang')

# 2. 加载数据
import pandas as pd
import json

# 读取区域信息
with open(config['region2allinfo_path'], 'r') as f:
    region_info = json.load(f)

# 读取数据集划分
train_df = pd.read_csv(config['train_idx_path'])
val_df = pd.read_csv(config['val_idx_path'])  
test_df = pd.read_csv(config['test_idx_path'])

# 3. 运行模型
# python main.py --model RGCN --dataset shenyang --epochs 50
```

### 数据质量检查

命令行运行:
```bash
# 运行综合数据质量检查
python unified_data_checker.py
```

## ⚠️ 注意事项

1. **数据对齐**: 确保CSV中的BlockID与region_info中的key格式匹配
2. **能耗标签**: 目标变量存储在region_info的'energy'字段中
3. **图像数据**: 卫星图像文件名格式为`{BlockID}.png`
4. **知识图谱**: 实体命名采用`Region_{BlockID}`格式
5. **预训练嵌入**: 确保嵌入维度与图中实体数量匹配

## 📞 联系方式

如有问题请参考数据质量检查报告或联系数据集维护者。

---

*此文档由数据质量检查工具自动生成*
