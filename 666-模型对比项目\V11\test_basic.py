#!/usr/bin/env python3
"""
Basic test to check if the data files exist and can be loaded
"""

import os
import json
import pandas as pd
from pathlib import Path

def test_data_files():
    """Test if data files exist and can be loaded"""
    print("🔍 Testing data file accessibility...")
    
    data_dir = Path("D:/研二/能耗估算/666-模型对比项目/V11/data/shenyang")
    
    # Test file paths
    files_to_test = {
        'region2allinfo_path': data_dir / 'shenyang_region2allinfo.json',
        'kg_path': data_dir / 'kg_cuda_complete_21_relations_optimized.txt',
        'poi_streetview_filename_path': data_dir / 'streetview_image' / 'region_5_10_poi_image_filename.json',
        'train_idx_path': data_dir / 'shenyang_zl15_train.csv',
        'val_idx_path': data_dir / 'shenyang_zl15_valid.csv',
        'test_idx_path': data_dir / 'shenyang_zl15_test.csv',
    }
    
    for name, path in files_to_test.items():
        print(f"\n📁 Testing {name}:")
        print(f"   Path: {path}")
        
        if path.exists():
            print(f"   ✅ File exists")
            
            try:
                if path.suffix == '.json':
                    with open(path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    print(f"   ✅ JSON loaded successfully: {len(data)} items")
                    
                elif path.suffix == '.csv':
                    df = pd.read_csv(path)
                    print(f"   ✅ CSV loaded successfully: {len(df)} rows, columns: {list(df.columns)}")
                    
                elif path.suffix == '.txt':
                    with open(path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                    print(f"   ✅ TXT loaded successfully: {len(lines)} lines")
                    
            except Exception as e:
                print(f"   ❌ Error loading file: {e}")
        else:
            print(f"   ❌ File does not exist")

if __name__ == "__main__":
    test_data_files()
    print("\n🎉 Basic test completed!")
