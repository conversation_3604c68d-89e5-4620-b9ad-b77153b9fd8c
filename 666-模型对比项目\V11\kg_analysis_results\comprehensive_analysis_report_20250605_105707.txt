
================================================================================
知识图谱增强版分析报告
================================================================================
生成时间: 2025-06-05 10:57:07
知识图谱文件: D:\研二\能耗估算\666-模型对比项目\V11\data\shenyang\kg_cuda_complete_21_relations_optimized.txt

1. 基本统计信息
--------------------------------------------------
三元组总数: 727,578
实体总数: 200,558
关系类型数: 19

2. 实体类型分布
--------------------------------------------------
POI            :  110,910 ( 55.3%)
Building       :   86,647 ( 43.2%)
Land           :    2,187 (  1.1%)
Region         :      757 (  0.4%)
Category       :       15 (  0.0%)
Unknown        :       13 (  0.0%)
Morphology     :       11 (  0.0%)
BusinessCircle :        9 (  0.0%)
Function       :        6 (  0.0%)
LandUse        :        3 (  0.0%)

3. 关系类型分布 (前10)
--------------------------------------------------
functionalSimilarity     : 198,441 ( 27.3%)
cateOf                   : 110,910 ( 15.2%)
locateAt                 : 110,910 ( 15.2%)
hasPhysicalAttribute     :  86,647 ( 11.9%)
hasFunction              :  86,647 ( 11.9%)
belongsToLand            :  78,283 ( 10.8%)
connectedTo              :  37,140 (  5.1%)
nearBy                   :   6,708 (  0.9%)
densityInfluence         :   3,579 (  0.5%)
highConvenience          :   2,737 (  0.4%)

4. ID对齐分析结果
--------------------------------------------------
数据集区域总数: 757
完全对齐ID数量: 757
完全对齐率: 100.0%

各数据源对齐情况:
  ✅ 知识图谱        : 100.0% (757/757)
  ✅ 区域信息        : 100.0% (757/757)
  ✅ POI映射       : 100.0% (757/757)
  ✅ 卫星图像        : 100.0% (757/757)
  ✅ 街景图像        : 100.0% (757/757)

街区ID覆盖范围:
  知识图谱ID范围: [1, 757]
  数据集ID范围: [1, 757]

5. 详细类别统计
--------------------------------------------------
建筑功能类别: 6 种
建筑形态类别: 11 种
土地利用类别: 3 种
POI类别: 15 种

6. 发现的问题
--------------------------------------------------
未发现严重问题。

7. 修复建议
--------------------------------------------------
暂无特别建议。

================================================================================
报告结束 - 详细信息请查看日志文件
================================================================================
