{"POI": {"count": 110910, "entities": ["POI_1000", "POI_1001", "POI_1002", "POI_1003", "POI_1004", "POI_1005", "POI_1006", "POI_1007", "POI_1008", "POI_1009", "POI_1010", "POI_1011", "POI_1012", "POI_1013", "POI_1014", "POI_1015", "POI_1016", "POI_1017", "POI_1018", "POI_1019", "POI_1020", "POI_1021", "POI_1022", "POI_1023", "POI_1024", "POI_1025", "POI_1026", "POI_1027", "POI_1028", "POI_1029", "POI_102985", "POI_102986", "POI_102988", "POI_102989", "POI_102990", "POI_102992", "POI_102993", "POI_102994", "POI_102996", "POI_102997", "POI_102998", "POI_102999", "POI_1030", "POI_103000", "POI_103001", "POI_103002", "POI_103003", "POI_103004", "POI_103005", "POI_103006", "POI_103007", "POI_103008", "POI_103009", "POI_103010", "POI_103011", "POI_103012", "POI_103013", "POI_103014", "POI_103015", "POI_103016", "POI_103018", "POI_103019", "POI_103020", "POI_103022", "POI_103023", "POI_103050", "POI_103051", "POI_103052", "POI_103053", "POI_103054", "POI_103055", "POI_103056", "POI_103057", "POI_103058", "POI_103059", "POI_103060", "POI_103061", "POI_103062", "POI_103063", "POI_103064", "POI_103065", "POI_103066", "POI_103067", "POI_103068", "POI_103069", "POI_103070", "POI_103071", "POI_103072", "POI_103073", "POI_103074", "POI_103075", "POI_103076", "POI_103077", "POI_103078", "POI_103079", "POI_103080", "POI_103081", "POI_103082", "POI_103083", "POI_103084"]}, "Building": {"count": 86647, "entities": ["Building_0", "Building_1", "Building_10", "Building_100", "Building_1000", "Building_10000", "Building_10002", "Building_10003", "Building_10004", "Building_10005", "Building_10006", "Building_10007", "Building_10008", "Building_10009", "Building_1001", "Building_10010", "Building_10011", "Building_10012", "Building_10013", "Building_10014", "Building_10015", "Building_10016", "Building_10017", "Building_10018", "Building_10019", "Building_1002", "Building_10020", "Building_10021", "Building_10022", "Building_10023", "Building_10024", "Building_10025", "Building_10026", "Building_10027", "Building_10028", "Building_10029", "Building_1003", "Building_10030", "Building_10031", "Building_10032", "Building_10033", "Building_10034", "Building_10035", "Building_10036", "Building_10037", "Building_10038", "Building_10039", "Building_1004", "Building_10040", "Building_10041", "Building_10042", "Building_10043", "Building_10044", "Building_10045", "Building_10046", "Building_10047", "Building_10048", "Building_10049", "Building_1005", "Building_10050", "Building_10051", "Building_10052", "Building_10053", "Building_10054", "Building_10055", "Building_10056", "Building_10057", "Building_10058", "Building_10059", "Building_1006", "Building_10060", "Building_10061", "Building_10062", "Building_10063", "Building_10064", "Building_10065", "Building_10066", "Building_10067", "Building_10068", "Building_10069", "Building_1007", "Building_10070", "Building_10071", "Building_10072", "Building_10073", "Building_10074", "Building_10075", "Building_10076", "Building_10077", "Building_10078", "Building_10079", "Building_1008", "Building_10080", "Building_10081", "Building_10082", "Building_10083", "Building_10084", "Building_10085", "Building_10086", "Building_10087"]}, "Land": {"count": 2187, "entities": ["Land_1", "Land_10", "Land_100", "Land_1000", "Land_1001", "Land_1002", "Land_1003", "Land_1004", "Land_1005", "Land_1006", "Land_1007", "Land_1008", "Land_1009", "Land_101", "Land_1010", "Land_1011", "Land_1012", "Land_1013", "Land_1014", "Land_1015", "Land_1016", "Land_1017", "Land_1018", "Land_1019", "Land_102", "Land_1020", "Land_1021", "Land_1022", "Land_1023", "Land_1024", "Land_1025", "Land_1026", "Land_1027", "Land_1028", "Land_1029", "Land_103", "Land_1030", "Land_1031", "Land_1032", "Land_1033", "Land_1034", "Land_1035", "Land_1036", "Land_1037", "Land_1038", "Land_1039", "Land_104", "Land_1040", "Land_1041", "Land_1042", "Land_1043", "Land_1044", "Land_1045", "Land_1046", "Land_1047", "Land_1048", "Land_1049", "Land_105", "Land_1050", "Land_1051", "Land_1052", "Land_1053", "Land_1054", "Land_1055", "Land_1056", "Land_1057", "Land_1058", "Land_1059", "Land_106", "Land_1060", "Land_1061", "Land_1062", "Land_1063", "Land_1064", "Land_1065", "Land_1066", "Land_1067", "Land_1068", "Land_1069", "Land_107", "Land_1070", "Land_1071", "Land_1072", "Land_1073", "Land_1074", "Land_1075", "Land_1076", "Land_1077", "Land_1078", "Land_1079", "Land_108", "Land_1080", "Land_1081", "Land_1082", "Land_1083", "Land_1084", "Land_1085", "Land_1086", "Land_1087", "Land_1088"]}, "Region": {"count": 757, "entities": ["Region_1", "Region_10", "Region_100", "Region_101", "Region_102", "Region_103", "Region_104", "Region_105", "Region_106", "Region_107", "Region_108", "Region_109", "Region_11", "Region_110", "Region_111", "Region_112", "Region_113", "Region_114", "Region_115", "Region_116", "Region_117", "Region_118", "Region_119", "Region_12", "Region_120", "Region_121", "Region_122", "Region_123", "Region_124", "Region_125", "Region_126", "Region_127", "Region_128", "Region_129", "Region_13", "Region_130", "Region_131", "Region_132", "Region_133", "Region_134", "Region_135", "Region_136", "Region_137", "Region_138", "Region_139", "Region_14", "Region_140", "Region_141", "Region_142", "Region_143", "Region_144", "Region_145", "Region_146", "Region_147", "Region_148", "Region_149", "Region_15", "Region_150", "Region_151", "Region_152", "Region_153", "Region_154", "Region_155", "Region_156", "Region_157", "Region_158", "Region_159", "Region_16", "Region_160", "Region_161", "Region_162", "Region_163", "Region_164", "Region_165", "Region_166", "Region_167", "Region_168", "Region_169", "Region_17", "Region_170", "Region_171", "Region_172", "Region_173", "Region_174", "Region_175", "Region_176", "Region_177", "Region_178", "Region_179", "Region_18", "Region_180", "Region_181", "Region_182", "Region_183", "Region_184", "Region_185", "Region_186", "Region_187", "Region_188", "Region_189"]}, "Category": {"count": 15, "entities": ["Cate_Accommodation_Service", "Cate_Auto_Service", "Cate_Business_Residential", "Cate_Category_8", "Cate_Company_Enterprise", "Cate_Education_Culture", "Cate_Financial_Service", "Cate_Food_Service", "Cate_Government_Organization", "Cate_Healthcare_Service", "Cate_Life_Service", "Cate_Scenic_Spots", "Cate_Shopping_Service", "Cate_Sports_Recreation", "Cate_Transportation_Service"]}, "BusinessCircle": {"count": 9, "entities": ["BC_172", "BC_173", "BC_174", "BC_175", "BC_176", "BC_177", "BC_178", "BC_179", "BC_180"]}, "Unknown": {"count": 13, "entities": ["PhysicalAttr_MediumHighMid", "PhysicalAttr_MediumHighNew", "PhysicalAttr_MediumHighOld", "PhysicalAttr_MediumLowMid", "PhysicalAttr_MediumLowNew", "PhysicalAttr_MediumLowOld", "PhysicalAttr_MediumMidMid", "PhysicalAttr_MediumMidNew", "PhysicalAttr_MediumMidOld", "RegionFunc_Commercial", "RegionFunc_Education", "RegionFunc_Mixed", "RegionFunc_Residential"]}, "Morphology": {"count": 11, "entities": ["Morph_HighRiseHighDensity", "Morph_HighRiseLowDensity", "Morph_HighRiseMidDensity", "Morph_LowRiseHighDensity", "Morph_LowRiseLowDensity", "Morph_LowRiseMidDensity", "Morph_MidRiseHighDensity", "Morph_MidRiseLowDensity", "Morph_MidRiseMidDensity", "Morph_SuperHighRise", "Morph_Vacant"]}, "LandUse": {"count": 3, "entities": ["LandUse_Industrial", "LandUse_Other", "LandUse_Residential"]}, "Function": {"count": 6, "entities": ["Func_Commercial", "Func_Industrial", "Func_Office", "Func_Other", "Func_Public", "Func_Residential"]}}