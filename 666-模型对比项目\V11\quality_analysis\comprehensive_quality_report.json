{"data_loading": {"region_info_count": 757, "kg_entities_count": 200558, "kg_relations_count": 19, "kg_triples_count": 727578, "poi_mapping_count": 757, "train_count": 605, "val_count": 75, "test_count": 77, "total_unique_blocks": 757}, "dataset_format": {"info": {"dataset_name": "沈阳城市能耗预测数据集", "dataset_type": "多模态城市能耗预测", "task_type": "回归预测", "data_files": {"knowledge_graph": {"file": "D:\\研二\\能耗估算\\666-模型对比项目\\V11\\data\\shenyang\\kg_cuda_complete_21_relations_optimized.txt", "description": "知识图谱三元组数据", "format": "TSV文件 (制表符分隔)", "purpose": "构建城市区域间的关系网络", "schema": ["head_entity", "relation", "tail_entity"], "example": [["Building_13793", "belongsToLand", "Land_232"], ["POI_134249", "cateOf", "Cate_Education_Culture"], ["Building_466", "hasPhysicalAttribute", "PhysicalAttr_MediumLowNew"], ["Building_43956", "hasFunction", "Func_Residential"], ["POI_82858", "locateAt", "Region_640"]], "detailed_analysis": {"实体总数": 200558, "关系总数": 19, "三元组总数": 727578, "区域实体数": 757, "POI实体数": 110910, "实体类型分布": {"Other": 694454, "POI": 221820, "Region": 537345, "Facility": 1537}, "关系类型分布": {"belongsToLand": 78283, "cateOf": 110910, "hasPhysicalAttribute": 86647, "hasFunction": 86647, "locateAt": 110910, "provideService": 594, "functionalSimilarity": 198441, "connectedTo": 37140, "nearBy": 6708, "densityInfluence": 3579, "belongsToRegion": 526, "flowTransition": 663, "highConvenience": 2737, "hasMorphology": 2187, "hasDominantFunction": 757, "similarFunction": 146, "hasLandUse": 428, "functionalComplementarity": 151, "similarMorphology": 124}, "平均连接度": 7.26, "最频繁关系": ["functionalSimilarity", 198441]}}, "region_information": {"file": "D:\\研二\\能耗估算\\666-模型对比项目\\V11\\data\\shenyang\\shenyang_region2allinfo.json", "description": "区域详细信息和标签", "format": "JSON文件", "purpose": "提供区域的多维特征和能耗标签", "schema": ["cate1_poi_number", "cd", "center", "crime", "crime_cate", "edu", "edu_cate", "energy", "energy_cate", "income", "income_cate", "margin", "n_pois", "pop", "pop_cate", "unemploy", "unemploy_cate"], "example": {"1": {"energy": 0.23525684681895112, "n_pois": 0, "pop": 0, "center": [123.30830945492553, 41.704605184010795]}}}, "poi_image_mapping": {"file": "D:\\研二\\能耗估算\\666-模型对比项目\\V11\\data\\shenyang\\streetview_image\\region_5_10_poi_image_filename.json", "description": "POI街景图像文件名映射", "format": "JSON文件", "purpose": "建立区域与街景图像文件的对应关系", "schema": {"region_id": ["image_filename1", "image_filename2", "..."]}, "example": {"1": ["123.304666_41.7137313_0_0.jpg", "123.304666_41.7137313_180_0.jpg", "123.304666_41.7137313_270_0.jpg"], "10": ["123.3208513_41.7737655_0_0.jpg", "123.3208513_41.7737655_270_0.jpg", "123.3208513_41.7737655_90_0.jpg"], "100": ["123.3515128_41.77825676_0_0.jpg", "123.3515128_41.77825676_270_0.jpg", "123.3515128_41.77825676_90_0.jpg"]}, "detailed_analysis": {"映射区域数": 757, "总图像数": 30280, "平均每区域图像数": 40.0, "最大图像数": 40, "最小图像数": 40, "图像数量分布": {"中位数": 40, "标准差": 0.0}, "文件扩展名分布": {"jpg": 30280}, "文件名模式": {"123.304666": 3, "123.3050195": 3, "123.305373": 3, "123.3057266": 4, "123.3057791": 4}}}, "train_split": {"file": "D:\\研二\\能耗估算\\666-模型对比项目\\V11\\data\\shenyang\\shenyang_zl15_train.csv", "description": "训练集区域ID", "format": "CSV文件", "purpose": "指定用于模型训练的区域", "schema": ["BlockID"], "example": [410, 98, 282]}, "validation_split": {"file": "D:\\研二\\能耗估算\\666-模型对比项目\\V11\\data\\shenyang\\shenyang_zl15_valid.csv", "description": "验证集区域ID", "format": "CSV文件", "purpose": "模型验证和超参数调优", "schema": ["BlockID"], "example": [506, 746, 720]}, "test_split": {"file": "D:\\研二\\能耗估算\\666-模型对比项目\\V11\\data\\shenyang\\shenyang_zl15_test.csv", "description": "测试集区域ID", "format": "CSV文件", "purpose": "最终模型性能评估", "schema": ["BlockID"], "example": [202, 162, 556]}, "satellite_images": {"file": "D:\\研二\\能耗估算\\666-模型对比项目\\V11\\data\\shenyang\\satellite_image", "description": "卫星遥感图像", "format": "PNG图像文件", "purpose": "提供区域的空间视觉特征", "schema": "文件名格式: {BlockID}.png", "example": "示例: [1, 2, 3] -> ['1.png', '2.png', '3.png']"}, "streetview_images": {"file": "D:\\研二\\能耗估算\\666-模型对比项目\\V11\\data\\shenyang\\streetview_image", "description": "街景图像", "format": "目录结构的JPG/PNG文件", "purpose": "提供区域的街道级视觉特征", "schema": "目录结构: {BlockID}/image_files", "example": "每区域包含多张街景图片，通过POI映射文件关联"}, "pretrained_embeddings": {"file": "D:\\研二\\能耗估算\\666-模型对比项目\\V11\\data\\shenyang\\预训练数据\\ER_shenyangbb_TuckER_64.npz", "description": "预训练知识图谱嵌入", "format": "NPZ文件 (numpy压缩格式)", "purpose": "提供实体和关系的预训练向量表示", "schema": ["E_pretrain: 节点嵌入", "R_pretrain: 关系嵌入"], "example": "节点嵌入维度: 实体数×特征维度"}}, "data_schema": {}, "usage_examples": {}}, "data_files": {"knowledge_graph": {"file": "D:\\研二\\能耗估算\\666-模型对比项目\\V11\\data\\shenyang\\kg_cuda_complete_21_relations_optimized.txt", "description": "知识图谱三元组数据", "format": "TSV文件 (制表符分隔)", "purpose": "构建城市区域间的关系网络", "schema": ["head_entity", "relation", "tail_entity"], "example": [["Building_13793", "belongsToLand", "Land_232"], ["POI_134249", "cateOf", "Cate_Education_Culture"], ["Building_466", "hasPhysicalAttribute", "PhysicalAttr_MediumLowNew"], ["Building_43956", "hasFunction", "Func_Residential"], ["POI_82858", "locateAt", "Region_640"]], "detailed_analysis": {"实体总数": 200558, "关系总数": 19, "三元组总数": 727578, "区域实体数": 757, "POI实体数": 110910, "实体类型分布": {"Other": 694454, "POI": 221820, "Region": 537345, "Facility": 1537}, "关系类型分布": {"belongsToLand": 78283, "cateOf": 110910, "hasPhysicalAttribute": 86647, "hasFunction": 86647, "locateAt": 110910, "provideService": 594, "functionalSimilarity": 198441, "connectedTo": 37140, "nearBy": 6708, "densityInfluence": 3579, "belongsToRegion": 526, "flowTransition": 663, "highConvenience": 2737, "hasMorphology": 2187, "hasDominantFunction": 757, "similarFunction": 146, "hasLandUse": 428, "functionalComplementarity": 151, "similarMorphology": 124}, "平均连接度": 7.26, "最频繁关系": ["functionalSimilarity", 198441]}}, "region_information": {"file": "D:\\研二\\能耗估算\\666-模型对比项目\\V11\\data\\shenyang\\shenyang_region2allinfo.json", "description": "区域详细信息和标签", "format": "JSON文件", "purpose": "提供区域的多维特征和能耗标签", "schema": ["cate1_poi_number", "cd", "center", "crime", "crime_cate", "edu", "edu_cate", "energy", "energy_cate", "income", "income_cate", "margin", "n_pois", "pop", "pop_cate", "unemploy", "unemploy_cate"], "example": {"1": {"energy": 0.23525684681895112, "n_pois": 0, "pop": 0, "center": [123.30830945492553, 41.704605184010795]}}}, "poi_image_mapping": {"file": "D:\\研二\\能耗估算\\666-模型对比项目\\V11\\data\\shenyang\\streetview_image\\region_5_10_poi_image_filename.json", "description": "POI街景图像文件名映射", "format": "JSON文件", "purpose": "建立区域与街景图像文件的对应关系", "schema": {"region_id": ["image_filename1", "image_filename2", "..."]}, "example": {"1": ["123.304666_41.7137313_0_0.jpg", "123.304666_41.7137313_180_0.jpg", "123.304666_41.7137313_270_0.jpg"], "10": ["123.3208513_41.7737655_0_0.jpg", "123.3208513_41.7737655_270_0.jpg", "123.3208513_41.7737655_90_0.jpg"], "100": ["123.3515128_41.77825676_0_0.jpg", "123.3515128_41.77825676_270_0.jpg", "123.3515128_41.77825676_90_0.jpg"]}, "detailed_analysis": {"映射区域数": 757, "总图像数": 30280, "平均每区域图像数": 40.0, "最大图像数": 40, "最小图像数": 40, "图像数量分布": {"中位数": 40, "标准差": 0.0}, "文件扩展名分布": {"jpg": 30280}, "文件名模式": {"123.304666": 3, "123.3050195": 3, "123.305373": 3, "123.3057266": 4, "123.3057791": 4}}}, "train_split": {"file": "D:\\研二\\能耗估算\\666-模型对比项目\\V11\\data\\shenyang\\shenyang_zl15_train.csv", "description": "训练集区域ID", "format": "CSV文件", "purpose": "指定用于模型训练的区域", "schema": ["BlockID"], "example": [410, 98, 282]}, "validation_split": {"file": "D:\\研二\\能耗估算\\666-模型对比项目\\V11\\data\\shenyang\\shenyang_zl15_valid.csv", "description": "验证集区域ID", "format": "CSV文件", "purpose": "模型验证和超参数调优", "schema": ["BlockID"], "example": [506, 746, 720]}, "test_split": {"file": "D:\\研二\\能耗估算\\666-模型对比项目\\V11\\data\\shenyang\\shenyang_zl15_test.csv", "description": "测试集区域ID", "format": "CSV文件", "purpose": "最终模型性能评估", "schema": ["BlockID"], "example": [202, 162, 556]}, "satellite_images": {"file": "D:\\研二\\能耗估算\\666-模型对比项目\\V11\\data\\shenyang\\satellite_image", "description": "卫星遥感图像", "format": "PNG图像文件", "purpose": "提供区域的空间视觉特征", "schema": "文件名格式: {BlockID}.png", "example": "示例: [1, 2, 3] -> ['1.png', '2.png', '3.png']"}, "streetview_images": {"file": "D:\\研二\\能耗估算\\666-模型对比项目\\V11\\data\\shenyang\\streetview_image", "description": "街景图像", "format": "目录结构的JPG/PNG文件", "purpose": "提供区域的街道级视觉特征", "schema": "目录结构: {BlockID}/image_files", "example": "每区域包含多张街景图片，通过POI映射文件关联"}, "pretrained_embeddings": {"file": "D:\\研二\\能耗估算\\666-模型对比项目\\V11\\data\\shenyang\\预训练数据\\ER_shenyangbb_TuckER_64.npz", "description": "预训练知识图谱嵌入", "format": "NPZ文件 (numpy压缩格式)", "purpose": "提供实体和关系的预训练向量表示", "schema": ["E_pretrain: 节点嵌入", "R_pretrain: 关系嵌入"], "example": "节点嵌入维度: 实体数×特征维度"}}, "statistics": {"总区域数量": 757, "训练区域数": 605, "验证区域数": 75, "测试区域数": 77, "区域ID范围": "[1, 757]", "知识图谱规模": "200558 实体, 19 关系, 727578 三元组", "POI图像映射": "757 区域, 30280 张图像", "数据集划分比例": "训练79.9% : 验证9.9% : 测试10.2%"}, "workflow": ["1. 加载知识图谱 (kg_without_building.txt) 构建城市区域关系网络", "2. 读取区域信息 (region2allinfo.json) 获取特征和能耗标签", "3. 根据数据集划分文件 (train/val/test.csv) 分配训练数据", "4. 加载预训练嵌入 (TuckER_64.npz) 初始化模型参数", "5. 可选加载卫星和街景图像增强特征表示", "6. 训练图神经网络进行能耗预测"], "scenarios": ["城市能耗预测和分析", "多模态城市数据融合研究", "图神经网络在城市计算中的应用", "知识图谱增强的预测任务", "城市空间数据挖掘", "智慧城市能源管理系统"]}, "alignment_check": {"kg_aligned": 757, "energy_aligned": 757, "satellite_aligned": 0, "streetview_aligned": 757, "fully_aligned": 0, "missing_details": {"1": {"satellite_image": false}, "2": {"satellite_image": false}, "3": {"satellite_image": false}, "4": {"satellite_image": false}, "5": {"satellite_image": false}, "6": {"satellite_image": false}, "7": {"satellite_image": false}, "8": {"satellite_image": false}, "9": {"satellite_image": false}, "10": {"satellite_image": false}, "11": {"satellite_image": false}, "12": {"satellite_image": false}, "13": {"satellite_image": false}, "14": {"satellite_image": false}, "15": {"satellite_image": false}, "16": {"satellite_image": false}, "17": {"satellite_image": false}, "18": {"satellite_image": false}, "19": {"satellite_image": false}, "20": {"satellite_image": false}, "21": {"satellite_image": false}, "22": {"satellite_image": false}, "23": {"satellite_image": false}, "24": {"satellite_image": false}, "25": {"satellite_image": false}, "26": {"satellite_image": false}, "27": {"satellite_image": false}, "28": {"satellite_image": false}, "29": {"satellite_image": false}, "30": {"satellite_image": false}, "31": {"satellite_image": false}, "32": {"satellite_image": false}, "33": {"satellite_image": false}, "34": {"satellite_image": false}, "35": {"satellite_image": false}, "36": {"satellite_image": false}, "37": {"satellite_image": false}, "38": {"satellite_image": false}, "39": {"satellite_image": false}, "40": {"satellite_image": false}, "41": {"satellite_image": false}, "42": {"satellite_image": false}, "43": {"satellite_image": false}, "44": {"satellite_image": false}, "45": {"satellite_image": false}, "46": {"satellite_image": false}, "47": {"satellite_image": false}, "48": {"satellite_image": false}, "49": {"satellite_image": false}, "50": {"satellite_image": false}, "51": {"satellite_image": false}, "52": {"satellite_image": false}, "53": {"satellite_image": false}, "54": {"satellite_image": false}, "55": {"satellite_image": false}, "56": {"satellite_image": false}, "57": {"satellite_image": false}, "58": {"satellite_image": false}, "59": {"satellite_image": false}, "60": {"satellite_image": false}, "61": {"satellite_image": false}, "62": {"satellite_image": false}, "63": {"satellite_image": false}, "64": {"satellite_image": false}, "65": {"satellite_image": false}, "66": {"satellite_image": false}, "67": {"satellite_image": false}, "68": {"satellite_image": false}, "69": {"satellite_image": false}, "70": {"satellite_image": false}, "71": {"satellite_image": false}, "72": {"satellite_image": false}, "73": {"satellite_image": false}, "74": {"satellite_image": false}, "75": {"satellite_image": false}, "76": {"satellite_image": false}, "77": {"satellite_image": false}, "78": {"satellite_image": false}, "79": {"satellite_image": false}, "80": {"satellite_image": false}, "81": {"satellite_image": false}, "82": {"satellite_image": false}, "83": {"satellite_image": false}, "84": {"satellite_image": false}, "85": {"satellite_image": false}, "86": {"satellite_image": false}, "87": {"satellite_image": false}, "88": {"satellite_image": false}, "89": {"satellite_image": false}, "90": {"satellite_image": false}, "91": {"satellite_image": false}, "92": {"satellite_image": false}, "93": {"satellite_image": false}, "94": {"satellite_image": false}, "95": {"satellite_image": false}, "96": {"satellite_image": false}, "97": {"satellite_image": false}, "98": {"satellite_image": false}, "99": {"satellite_image": false}, "100": {"satellite_image": false}, "101": {"satellite_image": false}, "102": {"satellite_image": false}, "103": {"satellite_image": false}, "104": {"satellite_image": false}, "105": {"satellite_image": false}, "106": {"satellite_image": false}, "107": {"satellite_image": false}, "108": {"satellite_image": false}, "109": {"satellite_image": false}, "110": {"satellite_image": false}, "111": {"satellite_image": false}, "112": {"satellite_image": false}, "113": {"satellite_image": false}, "114": {"satellite_image": false}, "115": {"satellite_image": false}, "116": {"satellite_image": false}, "117": {"satellite_image": false}, "118": {"satellite_image": false}, "119": {"satellite_image": false}, "120": {"satellite_image": false}, "121": {"satellite_image": false}, "122": {"satellite_image": false}, "123": {"satellite_image": false}, "124": {"satellite_image": false}, "125": {"satellite_image": false}, "126": {"satellite_image": false}, "127": {"satellite_image": false}, "128": {"satellite_image": false}, "129": {"satellite_image": false}, "130": {"satellite_image": false}, "131": {"satellite_image": false}, "132": {"satellite_image": false}, "133": {"satellite_image": false}, "134": {"satellite_image": false}, "135": {"satellite_image": false}, "136": {"satellite_image": false}, "137": {"satellite_image": false}, "138": {"satellite_image": false}, "139": {"satellite_image": false}, "140": {"satellite_image": false}, "141": {"satellite_image": false}, "142": {"satellite_image": false}, "143": {"satellite_image": false}, "144": {"satellite_image": false}, "145": {"satellite_image": false}, "146": {"satellite_image": false}, "147": {"satellite_image": false}, "148": {"satellite_image": false}, "149": {"satellite_image": false}, "150": {"satellite_image": false}, "151": {"satellite_image": false}, "152": {"satellite_image": false}, "153": {"satellite_image": false}, "154": {"satellite_image": false}, "155": {"satellite_image": false}, "156": {"satellite_image": false}, "157": {"satellite_image": false}, "158": {"satellite_image": false}, "159": {"satellite_image": false}, "160": {"satellite_image": false}, "161": {"satellite_image": false}, "162": {"satellite_image": false}, "163": {"satellite_image": false}, "164": {"satellite_image": false}, "165": {"satellite_image": false}, "166": {"satellite_image": false}, "167": {"satellite_image": false}, "168": {"satellite_image": false}, "169": {"satellite_image": false}, "170": {"satellite_image": false}, "171": {"satellite_image": false}, "172": {"satellite_image": false}, "173": {"satellite_image": false}, "174": {"satellite_image": false}, "175": {"satellite_image": false}, "176": {"satellite_image": false}, "177": {"satellite_image": false}, "178": {"satellite_image": false}, "179": {"satellite_image": false}, "180": {"satellite_image": false}, "181": {"satellite_image": false}, "182": {"satellite_image": false}, "183": {"satellite_image": false}, "184": {"satellite_image": false}, "185": {"satellite_image": false}, "186": {"satellite_image": false}, "187": {"satellite_image": false}, "188": {"satellite_image": false}, "189": {"satellite_image": false}, "190": {"satellite_image": false}, "191": {"satellite_image": false}, "192": {"satellite_image": false}, "193": {"satellite_image": false}, "194": {"satellite_image": false}, "195": {"satellite_image": false}, "196": {"satellite_image": false}, "197": {"satellite_image": false}, "198": {"satellite_image": false}, "199": {"satellite_image": false}, "200": {"satellite_image": false}, "201": {"satellite_image": false}, "202": {"satellite_image": false}, "203": {"satellite_image": false}, "204": {"satellite_image": false}, "205": {"satellite_image": false}, "206": {"satellite_image": false}, "207": {"satellite_image": false}, "208": {"satellite_image": false}, "209": {"satellite_image": false}, "210": {"satellite_image": false}, "211": {"satellite_image": false}, "212": {"satellite_image": false}, "213": {"satellite_image": false}, "214": {"satellite_image": false}, "215": {"satellite_image": false}, "216": {"satellite_image": false}, "217": {"satellite_image": false}, "218": {"satellite_image": false}, "219": {"satellite_image": false}, "220": {"satellite_image": false}, "221": {"satellite_image": false}, "222": {"satellite_image": false}, "223": {"satellite_image": false}, "224": {"satellite_image": false}, "225": {"satellite_image": false}, "226": {"satellite_image": false}, "227": {"satellite_image": false}, "228": {"satellite_image": false}, "229": {"satellite_image": false}, "230": {"satellite_image": false}, "231": {"satellite_image": false}, "232": {"satellite_image": false}, "233": {"satellite_image": false}, "234": {"satellite_image": false}, "235": {"satellite_image": false}, "236": {"satellite_image": false}, "237": {"satellite_image": false}, "238": {"satellite_image": false}, "239": {"satellite_image": false}, "240": {"satellite_image": false}, "241": {"satellite_image": false}, "242": {"satellite_image": false}, "243": {"satellite_image": false}, "244": {"satellite_image": false}, "245": {"satellite_image": false}, "246": {"satellite_image": false}, "247": {"satellite_image": false}, "248": {"satellite_image": false}, "249": {"satellite_image": false}, "250": {"satellite_image": false}, "251": {"satellite_image": false}, "252": {"satellite_image": false}, "253": {"satellite_image": false}, "254": {"satellite_image": false}, "255": {"satellite_image": false}, "256": {"satellite_image": false}, "257": {"satellite_image": false}, "258": {"satellite_image": false}, "259": {"satellite_image": false}, "260": {"satellite_image": false}, "261": {"satellite_image": false}, "262": {"satellite_image": false}, "263": {"satellite_image": false}, "264": {"satellite_image": false}, "265": {"satellite_image": false}, "266": {"satellite_image": false}, "267": {"satellite_image": false}, "268": {"satellite_image": false}, "269": {"satellite_image": false}, "270": {"satellite_image": false}, "271": {"satellite_image": false}, "272": {"satellite_image": false}, "273": {"satellite_image": false}, "274": {"satellite_image": false}, "275": {"satellite_image": false}, "276": {"satellite_image": false}, "277": {"satellite_image": false}, "278": {"satellite_image": false}, "279": {"satellite_image": false}, "280": {"satellite_image": false}, "281": {"satellite_image": false}, "282": {"satellite_image": false}, "283": {"satellite_image": false}, "284": {"satellite_image": false}, "285": {"satellite_image": false}, "286": {"satellite_image": false}, "287": {"satellite_image": false}, "288": {"satellite_image": false}, "289": {"satellite_image": false}, "290": {"satellite_image": false}, "291": {"satellite_image": false}, "292": {"satellite_image": false}, "293": {"satellite_image": false}, "294": {"satellite_image": false}, "295": {"satellite_image": false}, "296": {"satellite_image": false}, "297": {"satellite_image": false}, "298": {"satellite_image": false}, "299": {"satellite_image": false}, "300": {"satellite_image": false}, "301": {"satellite_image": false}, "302": {"satellite_image": false}, "303": {"satellite_image": false}, "304": {"satellite_image": false}, "305": {"satellite_image": false}, "306": {"satellite_image": false}, "307": {"satellite_image": false}, "308": {"satellite_image": false}, "309": {"satellite_image": false}, "310": {"satellite_image": false}, "311": {"satellite_image": false}, "312": {"satellite_image": false}, "313": {"satellite_image": false}, "314": {"satellite_image": false}, "315": {"satellite_image": false}, "316": {"satellite_image": false}, "317": {"satellite_image": false}, "318": {"satellite_image": false}, "319": {"satellite_image": false}, "320": {"satellite_image": false}, "321": {"satellite_image": false}, "322": {"satellite_image": false}, "323": {"satellite_image": false}, "324": {"satellite_image": false}, "325": {"satellite_image": false}, "326": {"satellite_image": false}, "327": {"satellite_image": false}, "328": {"satellite_image": false}, "329": {"satellite_image": false}, "330": {"satellite_image": false}, "331": {"satellite_image": false}, "332": {"satellite_image": false}, "333": {"satellite_image": false}, "334": {"satellite_image": false}, "335": {"satellite_image": false}, "336": {"satellite_image": false}, "337": {"satellite_image": false}, "338": {"satellite_image": false}, "339": {"satellite_image": false}, "340": {"satellite_image": false}, "341": {"satellite_image": false}, "342": {"satellite_image": false}, "343": {"satellite_image": false}, "344": {"satellite_image": false}, "345": {"satellite_image": false}, "346": {"satellite_image": false}, "347": {"satellite_image": false}, "348": {"satellite_image": false}, "349": {"satellite_image": false}, "350": {"satellite_image": false}, "351": {"satellite_image": false}, "352": {"satellite_image": false}, "353": {"satellite_image": false}, "354": {"satellite_image": false}, "355": {"satellite_image": false}, "356": {"satellite_image": false}, "357": {"satellite_image": false}, "358": {"satellite_image": false}, "359": {"satellite_image": false}, "360": {"satellite_image": false}, "361": {"satellite_image": false}, "362": {"satellite_image": false}, "363": {"satellite_image": false}, "364": {"satellite_image": false}, "365": {"satellite_image": false}, "366": {"satellite_image": false}, "367": {"satellite_image": false}, "368": {"satellite_image": false}, "369": {"satellite_image": false}, "370": {"satellite_image": false}, "371": {"satellite_image": false}, "372": {"satellite_image": false}, "373": {"satellite_image": false}, "374": {"satellite_image": false}, "375": {"satellite_image": false}, "376": {"satellite_image": false}, "377": {"satellite_image": false}, "378": {"satellite_image": false}, "379": {"satellite_image": false}, "380": {"satellite_image": false}, "381": {"satellite_image": false}, "382": {"satellite_image": false}, "383": {"satellite_image": false}, "384": {"satellite_image": false}, "385": {"satellite_image": false}, "386": {"satellite_image": false}, "387": {"satellite_image": false}, "388": {"satellite_image": false}, "389": {"satellite_image": false}, "390": {"satellite_image": false}, "391": {"satellite_image": false}, "392": {"satellite_image": false}, "393": {"satellite_image": false}, "394": {"satellite_image": false}, "395": {"satellite_image": false}, "396": {"satellite_image": false}, "397": {"satellite_image": false}, "398": {"satellite_image": false}, "399": {"satellite_image": false}, "400": {"satellite_image": false}, "401": {"satellite_image": false}, "402": {"satellite_image": false}, "403": {"satellite_image": false}, "404": {"satellite_image": false}, "405": {"satellite_image": false}, "406": {"satellite_image": false}, "407": {"satellite_image": false}, "408": {"satellite_image": false}, "409": {"satellite_image": false}, "410": {"satellite_image": false}, "411": {"satellite_image": false}, "412": {"satellite_image": false}, "413": {"satellite_image": false}, "414": {"satellite_image": false}, "415": {"satellite_image": false}, "416": {"satellite_image": false}, "417": {"satellite_image": false}, "418": {"satellite_image": false}, "419": {"satellite_image": false}, "420": {"satellite_image": false}, "421": {"satellite_image": false}, "422": {"satellite_image": false}, "423": {"satellite_image": false}, "424": {"satellite_image": false}, "425": {"satellite_image": false}, "426": {"satellite_image": false}, "427": {"satellite_image": false}, "428": {"satellite_image": false}, "429": {"satellite_image": false}, "430": {"satellite_image": false}, "431": {"satellite_image": false}, "432": {"satellite_image": false}, "433": {"satellite_image": false}, "434": {"satellite_image": false}, "435": {"satellite_image": false}, "436": {"satellite_image": false}, "437": {"satellite_image": false}, "438": {"satellite_image": false}, "439": {"satellite_image": false}, "440": {"satellite_image": false}, "441": {"satellite_image": false}, "442": {"satellite_image": false}, "443": {"satellite_image": false}, "444": {"satellite_image": false}, "445": {"satellite_image": false}, "446": {"satellite_image": false}, "447": {"satellite_image": false}, "448": {"satellite_image": false}, "449": {"satellite_image": false}, "450": {"satellite_image": false}, "451": {"satellite_image": false}, "452": {"satellite_image": false}, "453": {"satellite_image": false}, "454": {"satellite_image": false}, "455": {"satellite_image": false}, "456": {"satellite_image": false}, "457": {"satellite_image": false}, "458": {"satellite_image": false}, "459": {"satellite_image": false}, "460": {"satellite_image": false}, "461": {"satellite_image": false}, "462": {"satellite_image": false}, "463": {"satellite_image": false}, "464": {"satellite_image": false}, "465": {"satellite_image": false}, "466": {"satellite_image": false}, "467": {"satellite_image": false}, "468": {"satellite_image": false}, "469": {"satellite_image": false}, "470": {"satellite_image": false}, "471": {"satellite_image": false}, "472": {"satellite_image": false}, "473": {"satellite_image": false}, "474": {"satellite_image": false}, "475": {"satellite_image": false}, "476": {"satellite_image": false}, "477": {"satellite_image": false}, "478": {"satellite_image": false}, "479": {"satellite_image": false}, "480": {"satellite_image": false}, "481": {"satellite_image": false}, "482": {"satellite_image": false}, "483": {"satellite_image": false}, "484": {"satellite_image": false}, "485": {"satellite_image": false}, "486": {"satellite_image": false}, "487": {"satellite_image": false}, "488": {"satellite_image": false}, "489": {"satellite_image": false}, "490": {"satellite_image": false}, "491": {"satellite_image": false}, "492": {"satellite_image": false}, "493": {"satellite_image": false}, "494": {"satellite_image": false}, "495": {"satellite_image": false}, "496": {"satellite_image": false}, "497": {"satellite_image": false}, "498": {"satellite_image": false}, "499": {"satellite_image": false}, "500": {"satellite_image": false}, "501": {"satellite_image": false}, "502": {"satellite_image": false}, "503": {"satellite_image": false}, "504": {"satellite_image": false}, "505": {"satellite_image": false}, "506": {"satellite_image": false}, "507": {"satellite_image": false}, "508": {"satellite_image": false}, "509": {"satellite_image": false}, "510": {"satellite_image": false}, "511": {"satellite_image": false}, "512": {"satellite_image": false}, "513": {"satellite_image": false}, "514": {"satellite_image": false}, "515": {"satellite_image": false}, "516": {"satellite_image": false}, "517": {"satellite_image": false}, "518": {"satellite_image": false}, "519": {"satellite_image": false}, "520": {"satellite_image": false}, "521": {"satellite_image": false}, "522": {"satellite_image": false}, "523": {"satellite_image": false}, "524": {"satellite_image": false}, "525": {"satellite_image": false}, "526": {"satellite_image": false}, "527": {"satellite_image": false}, "528": {"satellite_image": false}, "529": {"satellite_image": false}, "530": {"satellite_image": false}, "531": {"satellite_image": false}, "532": {"satellite_image": false}, "533": {"satellite_image": false}, "534": {"satellite_image": false}, "535": {"satellite_image": false}, "536": {"satellite_image": false}, "537": {"satellite_image": false}, "538": {"satellite_image": false}, "539": {"satellite_image": false}, "540": {"satellite_image": false}, "541": {"satellite_image": false}, "542": {"satellite_image": false}, "543": {"satellite_image": false}, "544": {"satellite_image": false}, "545": {"satellite_image": false}, "546": {"satellite_image": false}, "547": {"satellite_image": false}, "548": {"satellite_image": false}, "549": {"satellite_image": false}, "550": {"satellite_image": false}, "551": {"satellite_image": false}, "552": {"satellite_image": false}, "553": {"satellite_image": false}, "554": {"satellite_image": false}, "555": {"satellite_image": false}, "556": {"satellite_image": false}, "557": {"satellite_image": false}, "558": {"satellite_image": false}, "559": {"satellite_image": false}, "560": {"satellite_image": false}, "561": {"satellite_image": false}, "562": {"satellite_image": false}, "563": {"satellite_image": false}, "564": {"satellite_image": false}, "565": {"satellite_image": false}, "566": {"satellite_image": false}, "567": {"satellite_image": false}, "568": {"satellite_image": false}, "569": {"satellite_image": false}, "570": {"satellite_image": false}, "571": {"satellite_image": false}, "572": {"satellite_image": false}, "573": {"satellite_image": false}, "574": {"satellite_image": false}, "575": {"satellite_image": false}, "576": {"satellite_image": false}, "577": {"satellite_image": false}, "578": {"satellite_image": false}, "579": {"satellite_image": false}, "580": {"satellite_image": false}, "581": {"satellite_image": false}, "582": {"satellite_image": false}, "583": {"satellite_image": false}, "584": {"satellite_image": false}, "585": {"satellite_image": false}, "586": {"satellite_image": false}, "587": {"satellite_image": false}, "588": {"satellite_image": false}, "589": {"satellite_image": false}, "590": {"satellite_image": false}, "591": {"satellite_image": false}, "592": {"satellite_image": false}, "593": {"satellite_image": false}, "594": {"satellite_image": false}, "595": {"satellite_image": false}, "596": {"satellite_image": false}, "597": {"satellite_image": false}, "598": {"satellite_image": false}, "599": {"satellite_image": false}, "600": {"satellite_image": false}, "601": {"satellite_image": false}, "602": {"satellite_image": false}, "603": {"satellite_image": false}, "604": {"satellite_image": false}, "605": {"satellite_image": false}, "606": {"satellite_image": false}, "607": {"satellite_image": false}, "608": {"satellite_image": false}, "609": {"satellite_image": false}, "610": {"satellite_image": false}, "611": {"satellite_image": false}, "612": {"satellite_image": false}, "613": {"satellite_image": false}, "614": {"satellite_image": false}, "615": {"satellite_image": false}, "616": {"satellite_image": false}, "617": {"satellite_image": false}, "618": {"satellite_image": false}, "619": {"satellite_image": false}, "620": {"satellite_image": false}, "621": {"satellite_image": false}, "622": {"satellite_image": false}, "623": {"satellite_image": false}, "624": {"satellite_image": false}, "625": {"satellite_image": false}, "626": {"satellite_image": false}, "627": {"satellite_image": false}, "628": {"satellite_image": false}, "629": {"satellite_image": false}, "630": {"satellite_image": false}, "631": {"satellite_image": false}, "632": {"satellite_image": false}, "633": {"satellite_image": false}, "634": {"satellite_image": false}, "635": {"satellite_image": false}, "636": {"satellite_image": false}, "637": {"satellite_image": false}, "638": {"satellite_image": false}, "639": {"satellite_image": false}, "640": {"satellite_image": false}, "641": {"satellite_image": false}, "642": {"satellite_image": false}, "643": {"satellite_image": false}, "644": {"satellite_image": false}, "645": {"satellite_image": false}, "646": {"satellite_image": false}, "647": {"satellite_image": false}, "648": {"satellite_image": false}, "649": {"satellite_image": false}, "650": {"satellite_image": false}, "651": {"satellite_image": false}, "652": {"satellite_image": false}, "653": {"satellite_image": false}, "654": {"satellite_image": false}, "655": {"satellite_image": false}, "656": {"satellite_image": false}, "657": {"satellite_image": false}, "658": {"satellite_image": false}, "659": {"satellite_image": false}, "660": {"satellite_image": false}, "661": {"satellite_image": false}, "662": {"satellite_image": false}, "663": {"satellite_image": false}, "664": {"satellite_image": false}, "665": {"satellite_image": false}, "666": {"satellite_image": false}, "667": {"satellite_image": false}, "668": {"satellite_image": false}, "669": {"satellite_image": false}, "670": {"satellite_image": false}, "671": {"satellite_image": false}, "672": {"satellite_image": false}, "673": {"satellite_image": false}, "674": {"satellite_image": false}, "675": {"satellite_image": false}, "676": {"satellite_image": false}, "677": {"satellite_image": false}, "678": {"satellite_image": false}, "679": {"satellite_image": false}, "680": {"satellite_image": false}, "681": {"satellite_image": false}, "682": {"satellite_image": false}, "683": {"satellite_image": false}, "684": {"satellite_image": false}, "685": {"satellite_image": false}, "686": {"satellite_image": false}, "687": {"satellite_image": false}, "688": {"satellite_image": false}, "689": {"satellite_image": false}, "690": {"satellite_image": false}, "691": {"satellite_image": false}, "692": {"satellite_image": false}, "693": {"satellite_image": false}, "694": {"satellite_image": false}, "695": {"satellite_image": false}, "696": {"satellite_image": false}, "697": {"satellite_image": false}, "698": {"satellite_image": false}, "699": {"satellite_image": false}, "700": {"satellite_image": false}, "701": {"satellite_image": false}, "702": {"satellite_image": false}, "703": {"satellite_image": false}, "704": {"satellite_image": false}, "705": {"satellite_image": false}, "706": {"satellite_image": false}, "707": {"satellite_image": false}, "708": {"satellite_image": false}, "709": {"satellite_image": false}, "710": {"satellite_image": false}, "711": {"satellite_image": false}, "712": {"satellite_image": false}, "713": {"satellite_image": false}, "714": {"satellite_image": false}, "715": {"satellite_image": false}, "716": {"satellite_image": false}, "717": {"satellite_image": false}, "718": {"satellite_image": false}, "719": {"satellite_image": false}, "720": {"satellite_image": false}, "721": {"satellite_image": false}, "722": {"satellite_image": false}, "723": {"satellite_image": false}, "724": {"satellite_image": false}, "725": {"satellite_image": false}, "726": {"satellite_image": false}, "727": {"satellite_image": false}, "728": {"satellite_image": false}, "729": {"satellite_image": false}, "730": {"satellite_image": false}, "731": {"satellite_image": false}, "732": {"satellite_image": false}, "733": {"satellite_image": false}, "734": {"satellite_image": false}, "735": {"satellite_image": false}, "736": {"satellite_image": false}, "737": {"satellite_image": false}, "738": {"satellite_image": false}, "739": {"satellite_image": false}, "740": {"satellite_image": false}, "741": {"satellite_image": false}, "742": {"satellite_image": false}, "743": {"satellite_image": false}, "744": {"satellite_image": false}, "745": {"satellite_image": false}, "746": {"satellite_image": false}, "747": {"satellite_image": false}, "748": {"satellite_image": false}, "749": {"satellite_image": false}, "750": {"satellite_image": false}, "751": {"satellite_image": false}, "752": {"satellite_image": false}, "753": {"satellite_image": false}, "754": {"satellite_image": false}, "755": {"satellite_image": false}, "756": {"satellite_image": false}, "757": {"satellite_image": false}}}, "quality_analysis": {"energy_quality": {"total_regions": 757, "valid_energy_count": 757, "missing_energy_count": 0, "zero_energy_count": 10, "energy_range": [0.0, 2.439598218078459], "energy_mean": 0.9950277696097821, "energy_std": 0.419475221316631, "outlier_count": 24, "outlier_percentage": 3.1704095112285335, "energy_types": {"float": 747, "int": 10}}, "missing_data": {}, "outliers": {}, "data_types": {}}, "distribution_analysis": {"train": {"count": 605, "mean": 1.0011807094696834, "std": 0.4278227822163968, "min": 0.0, "max": 2.439598218078459, "median": 0.9880231825494366, "q25": 0.7513158882585057, "q75": 1.2123750788086154, "values": [1.1832614772684686, 0.898018738041985, 1.4841460826024866, 0.9971988142310972, 1.3453049937466355, 0.7333971540072284, 1.1979207615677268, 1.1547336785889932, 0.9278062127656275, 1.0629611964695835, 1.0662546399527661, 0.7088082936601423, 0.8805594452188155, 0.4385915271612816, 1.113601784392119, 0.9653114228928004, 1.0878340536513704, 0.9352319083516097, 1.2214184196252629, 0.6784263566018837, 1.0207816426788248, 1.0509272299791694, 0.975436119724905, 1.0849930612522483, 0.5969984093544973, 1.2606987721570044, 0.9681144830784062, 2.289265684310572, 0.9290806936637203, 2.337991012251715, 1.192379352323566, 1.0849433120299024, 0.5934948208891718, 0.7517689435888886, 0.8179362498886796, 0.9059742512270554, 0.9092860218196643, 1.4290250795654096, 1.7999160481553014, 1.8848345007115392, 0.0, 1.115438757978674, 1.0190899458305684, 1.377999852861643, 1.1618683636802916, 0.8005312216811887, 0.6000904424559476, 0.8193117347301896, 1.218176357100158, 0.41138949993132157, 1.0180193840750495, 0.6776697237350686, 0.6456476137726832, 1.0813968367425923, 0.6212100199232609, 0.8001567893909215, 1.0574589822643825, 0.9377831471614287, 1.3244668687938133, 0.8647944989036673, 0.8446273780151633, 0.4982401420657393, 0.6507170167447136, 0.4765365630301671, 1.092346288743819, 0.8585357972766724, 0.9587881856304541, 1.2940949023055717, 0.7513158882585057, 2.2108875551634326, 0.9520145395196397, 0.7073379275803319, 0.4243572857611724, 1.0760095239653806, 0.8913838155845767, 0.9327076088991495, 0.9824148328154032, 1.3399296812071613, 1.7288810066314177, 0.5263238894132236, 1.0551639075049013, 0.36771254622181354, 1.2976593747617367, 1.1679522267164317, 1.6896762946940884, 0.7363984003544496, 1.6106085325788082, 0.7828529660579104, 0.7775352452838397, 0.7506346237158891, 1.3983846608603148, 1.0136419436208255, 1.3061171374014249, 1.1606868016300076, 0.9172046188674493, 0.26527485622740815, 1.0845530146045261, 0.8426486607495954, 0.957624919312099, 0.8373355579349422, 1.1619650863321895, 0.9196142304754875, 1.1971029509603812, 0.6648977935912279, 0.8711486499312437, 0.4734365436456468, 0.45967351908067294, 1.147462029698477, 0.08419510744533082, 0.9014004739868523, 0.7032146283513601, 1.7214992193992393, 0.37157062264273044, 0.1897716305462591, 0.724350894522708, 1.0976899166002048, 1.0153996889175314, 0.7265421582671018, 1.3117277753197896, 1.0167690458048138, 0.551944049507956, 0.4850109819736627, 0.9288490927635457, 1.1688073366035467, 0.5556175489612444, 0.46872599099402107, 1.1409239034005991, 1.1513523127587475, 0.8635733091406744, 1.5642869322603676, 1.8953371844651905, 1.8766261385237466, 0.978347868964855, 0.816531127734521, 1.2457464655861705, 0.7233572655561289, 1.309021035057202, 1.0800088107685086, 0.9446986002181085, 1.013140921193072, 1.1062241797275654, 0.9356946071207937, 0.9710000826188256, 0.9127250326154939, 0.7791890938252614, 0.0, 0.9290315812356734, 1.7593515473450385, 1.14859411464504, 0.8715176791818666, 0.0, 0.7415830090647516, 1.408376046497607, 1.4490555887467929, 0.41154087777936166, 0.660392685609597, 1.0681845890625843, 1.987518421029838, 0.9301440740249578, 1.686998421742258, 1.5542588096793002, 0.7709709231181622, 0.3691442665502659, 0.7925124713134933, 0.8046378614838168, 0.8159395299577773, 1.1993888694279626, 1.8992223842102225, 0.8080924873281886, 0.17068339634754076, 1.222919253019312, 0.7063796915704785, 1.094358320661902, 0.4778637045717106, 1.0668256801373956, 0.6414659344804665, 0.982901580921048, 0.16313063728300659, 1.0390587135034774, 0.0, 0.9351177435886872, 0.510407253648586, 0.6635649528803791, 0.876016118874107, 0.47171357514944523, 0.6333313475644187, 1.5593427873620627, 0.6910259476240891, 1.124368625554801, 1.1870478912879383, 1.173854506516275, 0.799755337798307, 1.6009840426383488, 0.5107015755655134, 1.0985631834527096, 0.6896485500336117, 1.1916410306105718, 1.1378977780903337, 1.3064661999326888, 1.0242959393862552, 0.911941909480981, 1.07274481230075, 1.0328338153164018, 1.8155648915362759, 0.9005339139172575, 0.9283907523626993, 1.5869008171179086, 0.49818557210193876, 0.1902050720568096, 1.0539231672400418, 1.0856241560889381, 1.3805026902078723, 0.9020072533479715, 0.1470379982142391, 1.1263193521297965, 0.6527130913824701, 0.9538721530517325, 1.9110823577731595, 1.1433478932973369, 1.1329840131527509, 0.23653018963783506, 0.14665796549305843, 0.576500211674429, 0.8146464757437841, 1.212566389065705, 0.7945217623688772, 0.9230968297800205, 1.201122813206592, 1.6386192427852735, 0.963880902626178, 1.1094289659212138, 1.0891440233358938, 1.537549687918335, 0.5152576187478394, 0.25615549047447017, 1.1421755212725624, 1.3065349419474075, 0.3613330430898962, 1.112044686557654, 0.8747536645151797, 1.9325887437940963, 0.8821982268689014, 1.7590153469322987, 1.3440948152621528, 0.9527434236094072, 1.1323890383900443, 1.6208676272345153, 1.2066120816181398, 1.0743266938620106, 1.3351469377265974, 1.1655022700570732, 1.2722318897048428, 1.6589522014810638, 1.1616287233586104, 0.766452874018715, 1.073525606785137, 0.35468262535354256, 0.8368655874033725, 0.7077411852852131, 1.0142125061098282, 1.3891288101807793, 1.2170128783727496, 0.8913191555854382, 0.0, 1.0326999485142099, 0.5799774896550028, 0.05992161873992977, 1.2896982605392755, 0.22754957551951704, 0.7448224879692691, 1.8855953980916067, 0.9597679156114524, 0.7492870662177005, 1.1551677327172618, 0.9285426896827395, 1.1662089369813144, 1.7077167157872524, 1.3166198013347512, 0.6124352875458057, 0.9566732698443513, 0.3510552195434595, 0.6852015960155381, 1.6667280134661626, 0.577823010990117, 1.523682231634592, 1.104563012886036, 0.9190853515685463, 0.8682792289815681, 1.006760631466999, 0.897813132373824, 0.4434820062325024, 0.8030338084064189, 0.8988490470362632, 0.9320441196783705, 0.5822392212622356, 0.46844108292442294, 1.347624086083418, 1.1881822509701665, 0.32771985361709, 1.01560356384115, 0.0, 0.6794511846896026, 1.4190078611184127, 1.145790741045359, 0.9285036373150872, 0.7623096157283373, 1.5041503700373684, 0.8928000040629419, 0.9269158941104148, 1.0592367290564482, 1.1829299078975688, 0.6504778204292114, 1.6179891111386169, 0.9406053591738326, 0.9400630020034518, 0.9841650816888368, 0.6215808397790887, 0.22111678325291403, 1.5714692863503241, 0.2256358753237548, 1.0054521090237372, 0.9118725833939405, 1.4249127359645808, 1.0460926083944964, 0.6805392742987182, 1.018344330396491, 1.0883404664467882, 0.782091950471126, 0.33113439787165083, 0.9316858277608342, 0.8615071486615598, 0.23525684681895112, 0.4320951674138675, 0.9903254792516878, 0.9329403125825395, 0.5466965690340866, 1.274345515144166, 1.8048635647487075, 1.0102851423032548, 1.5241054928012967, 1.287949146881846, 0.9106577563361249, 1.6239117480919276, 1.700780928750186, 0.264365759998004, 0.624399265664264, 1.4884913879156942, 1.0487674449981623, 0.6915976073189507, 0.5675139374900177, 0.9125801139359451, 1.0329790944147945, 2.1101615319170137, 2.1377769932116895, 1.576158608721789, 0.7979150234472889, 0.8340384246105065, 1.6803958131337449, 0.6197629736377539, 1.1152042523963626, 0.781334783072716, 1.0881656962896975, 0.9114773113606152, 0.8204313851103185, 0.7994163923611236, 0.9075714065406586, 1.1595075082934203, 0.9880231825494366, 2.094322605012628, 1.6680325683158748, 1.7657314574953618, 1.0465918679870763, 0.9086619995022968, 1.3969895608505907, 1.2284582413169343, 1.0332252690415262, 1.0903116494746294, 1.2917662899640572, 1.7717405487187612, 1.0714539417055633, 1.1256340535702212, 1.1531472671855802, 0.9649449896351012, 0.8365908799271359, 1.0946491730099743, 1.1088945035117022, 1.027983818329188, 1.2359390203373135, 1.4073364246728008, 1.036336231723425, 1.2793448295464382, 1.1207379795646222, 1.2547004709396044, 1.4405604010991302, 0.6846008515152946, 1.8778984372509742, 0.9552725608362626, 1.371574178907148, 1.1410420790209825, 1.8568445558051039, 0.9786694465395271, 0.4410321550085986, 1.8317809830844691, 1.4271657674877383, 0.9769380263376989, 0.34817273894871736, 1.3094070759158944, 1.8654026929549645, 0.23296323399823252, 0.0, 0.8185537865636825, 0.905863315880927, 0.7821811153335327, 1.1136601764777805, 1.1408406969726503, 1.0076557968619226, 0.5229042833518818, 1.5816749746422805, 0.9157166629905734, 1.291254283354212, 0.571822527940548, 0.7506481875757003, 0.8894617932475574, 0.46599059512511576, 1.5423387540640925, 1.338220376071025, 1.1250979239408492, 1.8616642813642663, 1.0196775880613873, 1.1060127521440215, 1.1588972381377887, 0.7659221228704618, 0.9046312104800303, 1.7092286493033415, 1.4624151052240335, 1.4125068364096862, 1.8024364508836979, 1.7030537103201977, 1.7322687139092654, 1.6399761126806114, 0.32454510195574315, 1.0919746132402635, 0.1967768361788318, 0.22238481627270282, 0.7921853167247808, 1.13805236493733, 0.13170274656918818, 1.6575624956984867, 1.061693886121196, 0.5920985281312026, 0.36482650502092584, 0.6863844508732154, 1.0600490827717317, 0.8112241636249875, 0.3942880582751397, 1.1792660688549563, 0.31439232623854246, 1.1465139171039562, 0.5993691250789346, 0.9254869577339372, 0.8381586917577989, 1.088202053938602, 0.36273462723297023, 1.1940592479244474, 1.0899957705268302, 2.439598218078459, 1.0497190818207807, 1.2210859128445517, 1.54685412569607, 0.6821936173386678, 1.5169438790036422, 0.820475822816225, 0.3564895862916338, 1.7499084400802594, 1.233263157712693, 0.7600918620080177, 0.8083507711779737, 0.9639787967194832, 1.0981542213679345, 1.3528592920266758, 0.9446347721352447, 1.0990297952394716, 1.1369287400952388, 1.1480518503078188, 0.6268536603641319, 1.3087200986139278, 0.8814187697267871, 1.1481393274359992, 1.2408540598399382, 1.645860357672865, 1.427010479331858, 1.4558918533965384, 0.23363344641607042, 0.7784740206292863, 1.2770861604920853, 0.9553383821938192, 1.4620891113489376, 0.8149851131946847, 1.7250291336187922, 1.730126729599214, 0.78708865812147, 1.999174620523051, 0.8123288141077365, 1.614948502568956, 1.9903985691776738, 1.402359441486768, 0.4902365136875413, 1.924805962630905, 0.9321437610090355, 0.6979914419244394, 0.701953598132674, 1.2091883960812757, 0.6971359771017479, 0.5898644521595392, 0.7968015770536161, 0.9299995896400455, 0.9046840826834734, 0.43844783154092015, 1.5630616970439788, 0.9023856717807847, 1.1478120620412355, 0.8369223673188277, 1.0131003395259293, 0.7010829313885616, 0.9707241100467544, 1.438286415482738, 0.7279961016544171, 0.9210732627702902, 0.18210542696334053, 0.6641688599268726, 0.1454067205024678, 0.47587931743221396, 1.1278921632774248, 1.11016279706167, 1.1052962652009968, 0.8513739507180875, 0.3982446403763661, 0.8156526531665197, 0.720321768073092, 1.3520210289919112, 0.8652440031591063, 1.3214496571449719, 0.5554984165897093, 1.2673870276532295, 1.252631873967636, 1.159421355571475, 0.6715592957985755, 1.288259491419164, 0.5580183357462403, 0.7527570209755677, 0.8391620589365392, 0.32870333939972224, 0.919941008187782, 1.6173737328694606, 1.0289922662493864, 1.6738533008957504, 0.9913542998143011, 1.1246278902856275, 1.0909540614586606, 0.7593076118782209, 1.599567040229682, 1.0495884028992333, 1.0197841084260866, 1.7387159788741666, 0.9160930243722248, 1.1577180318271383, 1.1496593213707502, 0.9934883750700849, 1.3401128138463876, 1.031400376806251, 0.8798774892994978, 0.613623249352815, 0.09626843980015548, 0.5194887056569519, 0.946208226973767, 1.8550988760315406, 0.8764370989092602, 1.8116967220883395, 0.7464039452829913, 0.7270970591752347, 0.6013682412566989, 0.15421975905145496, 0.803016211782207, 0.0, 1.2525682459631278, 1.0475192623318752, 0.7556387788518338, 1.1713183535107792, 0.7289911212184698, 0.9596698398438095, 1.1100502125444378, 1.432080882287103, 1.073076890784993, 1.2123750788086154, 1.11885434633055, 0.689046846154849, 1.3093924203557317, 1.0279263938728773, 1.0353084544642603, 0.9969687901411125, 1.335211995667853, 0.9539253558951211, 0.0, 0.44222701926608443, 1.242751494932059]}, "val": {"count": 75, "mean": 0.9811455679016492, "std": 0.3928110770869038, "min": 0.0, "max": 1.821865489072648, "median": 0.9989310960250134, "q25": 0.7857459072725315, "q75": 1.1754518193982115, "values": [0.5693023572081812, 1.331429806749735, 1.6190427717646128, 0.734644221874063, 1.2608600602760989, 1.821865489072648, 1.159122731552078, 1.139138499439985, 1.1579725646243282, 0.9445633810676305, 0.22666409100863955, 0.4941171866856136, 0.4850898083758863, 0.47015510078559236, 0.31423010138828017, 1.2855199224440816, 1.0397848304843609, 1.4437378693703198, 0.4384424171391942, 0.7277398069928256, 0.8620474722231005, 0.9989310960250134, 1.4177434832929583, 0.5617343159645422, 1.1201141685898812, 0.8646840473091777, 0.904363708552338, 1.1339471663907215, 1.0597226325242877, 1.4590117150278084, 0.30345567969410314, 1.1426797922794996, 1.130204755054245, 0.39454540525756887, 1.716051219712979, 0.8500605175561065, 0.9846069056726092, 0.8553276439118739, 0.9602692538359551, 1.1704195407414968, 1.007851917951237, 0.5159107853184822, 0.589611507921287, 1.5389786554206166, 1.1666284360250219, 0.9403126390670576, 1.102414602390568, 1.2448281593780284, 0.7911421680580465, 0.8780123218708937, 0.1556809002701158, 1.171448276236578, 0.540445372397693, 1.179455362559845, 0.9223016392762019, 1.1479448300403436, 1.0761715365944209, 1.4708538188034375, 0.0, 0.7884436105940223, 0.7935499491690927, 0.7830482039510405, 0.5571595254336056, 1.0505818678095038, 0.9208120722014297, 1.1972336729599506, 1.433148232243942, 1.066607871394811, 0.8629887867248988, 1.2808093057440602, 0.86194238351077, 1.7900025980918315, 1.7699149113899206, 0.8542422097271251, 1.58209592417739]}, "test": {"count": 77, "mean": 0.9602048671795195, "std": 0.37387891398520506, "min": 0.09397037264876167, "max": 1.6891620107789322, "median": 0.9871774957966934, "q25": 0.7789327418253481, "q75": 1.2450442453285357, "values": [0.8171730989724755, 0.6219341340641154, 0.7898518433876663, 1.1358468234523555, 1.2450442453285357, 1.0205841079253986, 0.24408153530112112, 1.340012302074912, 0.09397037264876167, 1.181227036980676, 0.12832795314925655, 0.8756395004293335, 1.2702165505901113, 0.5425000538638084, 0.41702738598427, 0.8196146127534669, 1.6325679991973123, 0.9978269838726005, 1.3922242101905091, 0.9869998309811239, 1.08064432787699, 1.3652949906525118, 0.7908103865367117, 1.0265746496604826, 1.0345167728432285, 1.0073673039196713, 1.2188149573923956, 1.55515287243428, 0.8893751930751675, 0.5894093490658201, 0.8986938408647529, 1.0630000531611574, 1.1044129791503188, 0.6510426499977723, 1.2483019937011905, 1.187264331837397, 1.0196983045306316, 0.5157779232149826, 1.1337417747239527, 0.2418461038478568, 1.4713939237800737, 0.9871774957966934, 1.2937636102239707, 0.7272509085078025, 1.396531840426333, 0.485570902024, 0.8564710387478045, 0.44061419440540645, 1.5374673302148079, 1.3245050917502517, 1.264021434808807, 0.9199554393498143, 1.4210242369948498, 1.1315353195357738, 0.9360992218566443, 0.7789327418253481, 1.047409415301666, 1.039057943082103, 0.8650850013446107, 0.3180106938604738, 0.9110467453447088, 1.6145323427139282, 0.979425896638511, 0.7287786883812721, 1.253998957522327, 0.9042567742966409, 1.6891620107789322, 1.493219813925097, 1.1515856230918533, 0.6080134730754221, 0.8472622734671509, 0.20236286587170021, 0.35089156278364664, 0.5382025854238416, 1.4299904451405645, 0.9014949026104971, 0.9152606882846106]}}, "image_availability": {"satellite_available": 0, "satellite_missing": 757, "satellite_availability_rate": 0.0}, "diagnosis": {"total_issues": 1, "issues": ["卫星图像缺失过多: 757"], "recommendations": ["补充缺失的卫星图像或使用图像插值", "先运行模型训练生成预测结果"], "priority_recommendations": ["中等优先级：数据清洗和预处理"]}}